#!/usr/bin/env python3
"""
修复数据库中的数据格式问题
解决 Pydantic 验证错误
"""

import sqlite3
import os
from datetime import datetime


def check_database_data():
    """检查数据库中的数据格式"""
    print("=== 检查数据库数据格式 ===\n")
    
    db_path = "crawling_tasks.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有任务数据
            cursor.execute("SELECT * FROM tasks")
            tasks = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(tasks)")
            columns = [row[1] for row in cursor.fetchall()]
            
            print(f"数据库列: {columns}")
            print(f"任务数量: {len(tasks)}")
            print()
            
            # 检查每个任务的数据格式
            for i, task in enumerate(tasks[:3]):  # 只检查前3个任务
                print(f"任务 {i+1}:")
                task_dict = dict(zip(columns, task))
                
                for col, value in task_dict.items():
                    print(f"  {col}: {value} (类型: {type(value).__name__})")
                
                # 检查问题字段
                problems = []
                
                # 检查 email_notifications_enabled
                if 'email_notifications_enabled' in task_dict:
                    val = task_dict['email_notifications_enabled']
                    if val not in [0, 1, True, False, None]:
                        problems.append(f"email_notifications_enabled: {val} (应该是布尔值)")
                
                # 检查 email_test_mode
                if 'email_test_mode' in task_dict:
                    val = task_dict['email_test_mode']
                    if val not in [0, 1, True, False, None]:
                        problems.append(f"email_test_mode: {val} (应该是布尔值)")
                
                # 检查 status
                if 'status' in task_dict:
                    val = task_dict['status']
                    valid_statuses = ['pending', 'running', 'stopped', 'completed', 'error']
                    if val not in valid_statuses:
                        problems.append(f"status: {val} (应该是 {valid_statuses} 之一)")
                
                # 检查 comment_count
                if 'comment_count' in task_dict:
                    val = task_dict['comment_count']
                    if val is not None and not isinstance(val, int):
                        problems.append(f"comment_count: {val} (应该是整数)")
                
                if problems:
                    print(f"  ❌ 发现问题:")
                    for problem in problems:
                        print(f"    - {problem}")
                else:
                    print(f"  ✅ 数据格式正常")
                
                print()
                
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")


def fix_database_data():
    """修复数据库中的数据格式问题"""
    print("=== 修复数据库数据格式 ===\n")
    
    db_path = "crawling_tasks.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 备份数据
            backup_table = f"tasks_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute(f"CREATE TABLE {backup_table} AS SELECT * FROM tasks")
            print(f"✅ 数据已备份到表: {backup_table}")
            
            # 修复数据
            fixes_applied = 0
            
            # 1. 修复 email_notifications_enabled 字段
            cursor.execute("""
                UPDATE tasks 
                SET email_notifications_enabled = 1 
                WHERE email_notifications_enabled NOT IN (0, 1) 
                   OR email_notifications_enabled IS NULL
            """)
            fixes_applied += cursor.rowcount
            print(f"✅ 修复 email_notifications_enabled: {cursor.rowcount} 行")
            
            # 2. 修复 email_test_mode 字段
            cursor.execute("""
                UPDATE tasks 
                SET email_test_mode = 1 
                WHERE email_test_mode NOT IN (0, 1) 
                   OR email_test_mode IS NULL
            """)
            fixes_applied += cursor.rowcount
            print(f"✅ 修复 email_test_mode: {cursor.rowcount} 行")
            
            # 3. 修复 status 字段
            cursor.execute("""
                UPDATE tasks 
                SET status = 'pending' 
                WHERE status NOT IN ('pending', 'running', 'stopped', 'completed', 'error')
                   OR status IS NULL
            """)
            fixes_applied += cursor.rowcount
            print(f"✅ 修复 status: {cursor.rowcount} 行")
            
            # 4. 修复 comment_count 字段
            cursor.execute("""
                UPDATE tasks 
                SET comment_count = 0 
                WHERE comment_count IS NULL
            """)
            fixes_applied += cursor.rowcount
            print(f"✅ 修复 comment_count: {cursor.rowcount} 行")
            
            # 5. 修复 flash_sales_found 字段
            cursor.execute("""
                UPDATE tasks 
                SET flash_sales_found = 0 
                WHERE flash_sales_found IS NULL
            """)
            fixes_applied += cursor.rowcount
            print(f"✅ 修复 flash_sales_found: {cursor.rowcount} 行")
            
            conn.commit()
            print(f"\n✅ 总共修复了 {fixes_applied} 个字段")
            
            return True
            
    except Exception as e:
        print(f"❌ 修复数据库失败: {e}")
        return False


def verify_fixes():
    """验证修复结果"""
    print("=== 验证修复结果 ===\n")
    
    db_path = "crawling_tasks.db"
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查修复后的数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN email_notifications_enabled IN (0, 1) THEN 1 ELSE 0 END) as valid_email_enabled,
                    SUM(CASE WHEN email_test_mode IN (0, 1) THEN 1 ELSE 0 END) as valid_email_test,
                    SUM(CASE WHEN status IN ('pending', 'running', 'stopped', 'completed', 'error') THEN 1 ELSE 0 END) as valid_status,
                    SUM(CASE WHEN comment_count IS NOT NULL THEN 1 ELSE 0 END) as valid_comment_count
                FROM tasks
            """)
            
            result = cursor.fetchone()
            total, valid_email_enabled, valid_email_test, valid_status, valid_comment_count = result
            
            print(f"总任务数: {total}")
            print(f"有效的 email_notifications_enabled: {valid_email_enabled}/{total}")
            print(f"有效的 email_test_mode: {valid_email_test}/{total}")
            print(f"有效的 status: {valid_status}/{total}")
            print(f"有效的 comment_count: {valid_comment_count}/{total}")
            
            if (valid_email_enabled == total and 
                valid_email_test == total and 
                valid_status == total and 
                valid_comment_count == total):
                print("\n✅ 所有数据格式都已修复")
                return True
            else:
                print("\n❌ 仍有数据格式问题")
                return False
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("数据库数据格式修复工具\n")
    
    # 1. 检查当前数据格式
    check_database_data()
    
    # 2. 修复数据格式
    if fix_database_data():
        # 3. 验证修复结果
        if verify_fixes():
            print("\n🎉 数据库数据格式修复完成！")
            print("✅ 现在 Pydantic 验证应该可以正常工作")
        else:
            print("\n❌ 修复验证失败")
    else:
        print("\n❌ 数据格式修复失败")


if __name__ == "__main__":
    main()
