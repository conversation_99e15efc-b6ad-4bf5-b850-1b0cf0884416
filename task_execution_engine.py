"""
Task Execution Engine for Web Crawling System
Orchestrates the execution of crawling tasks with real-time monitoring
"""

import asyncio
import threading
import time
import logging
from typing import Dict, Set, Optional
from urllib.parse import urlparse

from DrissionPage import ChromiumPage
from multi_forum_comment_extractor import MultiForumCommentExtractor
from page_navigation_manager import page_navigation_manager
from email_notification_service import real_email_service
# 导入优化的邮件服务
try:
    from optimized_email_service import optimized_email_service
    OPTIMIZED_EMAIL_AVAILABLE = True
except ImportError:
    OPTIMIZED_EMAIL_AVAILABLE = False

# 导入真实的邮件服务（备用）
try:
    from email_notification_service import real_email_service
    REAL_EMAIL_AVAILABLE = True
except ImportError:
    REAL_EMAIL_AVAILABLE = False

try:
    from improved_forum_crawler import CrawlerConfig, FlashSaleDetector, StateManager
except ImportError:
    print("Warning: improved_forum_crawler not found, using basic implementations")
    class CrawlerConfig:
        def __init__(self):
            self.page_load_delay = 0
    class FlashSaleDetector:
        def __init__(self, config):
            pass
        def analyze_comment(self, text):
            return "sale" in text.lower() or "offer" in text.lower()
    class StateManager:
        def __init__(self, config):
            pass
        def load_state(self):
            return {}
        def save_state(self, state):
            pass

from task_browser_manager import TaskBrowserManager

# Import TaskManager and TaskStatus with forward reference handling
TaskManager = None
TaskStatus = None

def set_task_manager_classes(tm_class, ts_enum):
    """Set TaskManager and TaskStatus classes to avoid circular imports"""
    global TaskManager, TaskStatus
    TaskManager = tm_class
    TaskStatus = ts_enum


class TaskExecutor:
    """Executes a single crawling task with real-time monitoring"""

    def __init__(self, task_id: str, config: CrawlerConfig,
                 browser_manager: TaskBrowserManager, task_manager):
        self.task_id = task_id
        self.config = config
        self.browser_manager = browser_manager
        self.task_manager = task_manager
        self.logger = logging.getLogger(f"task_executor.{task_id[:8]}")
        
        # Task state
        self.is_running = False
        self.page: Optional[ChromiumPage] = None
        self.flash_sale_detector = FlashSaleDetector(config)
        self.state_manager = StateManager(config)
        
        # Monitoring thread
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Statistics
        self.last_comment_count = 0
        self.total_flash_sales_found = 0
        self.last_processed_comment_content = ""

        # Page navigation
        self.current_page_url = None
        self.base_url = None
        self.current_page_num = 1
        self.forum_type = None
    
    async def start(self):
        """Start the task execution"""
        if self.is_running:
            self.logger.warning(f"Task {self.task_id} is already running")
            return
        
        try:
            # Get task details
            task = self.task_manager.get_task(self.task_id)

            # Update status to running
            self.task_manager.update_task_status(self.task_id, "running")

            # Initialize page navigation
            self._initialize_page_navigation(task.post_url)

            # Create browser tab for this task
            self.page = self.browser_manager.create_task_tab(
                self.task_id, self.current_page_url, task.forum_domain
            )

            # Load task state
            self._load_task_state(task.post_url)

            # Start monitoring thread
            self.is_running = True
            self.stop_event.clear()

            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                args=(task,),
                name=f"TaskMonitor-{self.task_id[:8]}",
                daemon=True
            )
            self.monitor_thread.start()

            self.logger.info(f"Started task {self.task_id} for {task.post_url}")

        except Exception as e:
            self.logger.error(f"Failed to start task {self.task_id}: {e}")

            # Clean up any partially created resources
            self.is_running = False
            if self.page:
                try:
                    self.browser_manager.close_task_tab(self.task_id)
                except Exception as cleanup_error:
                    self.logger.error(f"Error cleaning up task tab: {cleanup_error}")
                self.page = None

            # Update status to error
            self.task_manager.update_task_status(
                self.task_id, "error", str(e)
            )
            raise
    
    async def stop(self):
        """Stop the task execution"""
        if not self.is_running:
            return
        
        self.logger.info(f"Stopping task {self.task_id}")
        
        # Signal stop
        self.is_running = False
        self.stop_event.set()
        
        # Wait for monitoring thread to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        # Close browser tab
        if self.page:
            self.browser_manager.close_task_tab(self.task_id)
            self.page = None
        
        # Update status
        self.task_manager.update_task_status(self.task_id, "stopped")
        
        self.logger.info(f"Stopped task {self.task_id}")

    def _initialize_page_navigation(self, post_url: str):
        """初始化页面导航"""
        self.forum_type = page_navigation_manager.detect_forum_type(post_url)
        self.base_url, self.current_page_num = page_navigation_manager.parse_page_from_url(post_url)
        self.current_page_url = post_url

        # 如果从指定页面开始监控，计算之前的评论数量
        if self.current_page_num > 1:
            expected_comments = page_navigation_manager.get_expected_comment_count_for_page(
                self.current_page_num, self.forum_type
            )
            self.last_comment_count = expected_comments
            self.logger.info(
                f"从第 {self.current_page_num} 页开始监控，预期之前有 {expected_comments} 条评论"
            )

        self.logger.info(
            f"页面导航初始化: 论坛={self.forum_type}, 基础URL={self.base_url}, "
            f"当前页={self.current_page_num}, 当前URL={self.current_page_url}"
        )

    def _check_and_navigate_to_next_page(self, current_comment_count: int):
        """检查并导航到下一页"""
        if not self.page or not self.forum_type:
            return

        # 判断是否需要翻页
        should_navigate = page_navigation_manager.should_navigate_to_next_page(
            self.page, self.forum_type,
            expected_comments=current_comment_count,
            actual_comments=current_comment_count
        )

        if should_navigate:
            self.logger.info(f"检测到需要翻页，当前页: {self.current_page_num}")

            # 导航到下一页
            next_url = page_navigation_manager.navigate_to_next_page(
                self.page, self.current_page_url
            )

            if next_url:
                # 更新页面状态
                self.current_page_url = next_url
                self.current_page_num += 1
                # 翻页后，评论重新计算
                self.last_comment_count=0

                # 更新任务URL
                self.task_manager.update_task_url(self.task_id, next_url)

                self.logger.info(
                    f"成功翻页到第 {self.current_page_num} 页: {next_url}"
                )
            else:
                self.logger.warning("翻页失败，继续在当前页监控")

    def _monitoring_loop(self, task):
        """Main monitoring loop for the task"""
        self.logger.info(f"Starting monitoring loop for task {self.task_id} with interval {task.monitor_interval}s")

        consecutive_errors = 0
        max_consecutive_errors = 3

        try:
            while self.is_running and not self.stop_event.is_set():
                try:
                    # Perform monitoring cycle
                    self._perform_monitoring_cycle(task)

                    # Reset error counter on successful cycle
                    consecutive_errors = 0

                    # Wait for next cycle using the task's configured monitor_interval
                    self.logger.debug(f"Waiting {task.monitor_interval} seconds before next monitoring cycle")
                    if self.stop_event.wait(timeout=task.monitor_interval):
                        break  # Stop event was set

                except Exception as e:
                    consecutive_errors += 1
                    self.logger.error(f"Error in monitoring loop for task {self.task_id} (attempt {consecutive_errors}/{max_consecutive_errors}): {e}")

                    if consecutive_errors >= max_consecutive_errors:
                        self.logger.error(f"Too many consecutive errors, stopping task {self.task_id}")
                        self.task_manager.update_task_status(
                            self.task_id, "error", f"Too many consecutive errors: {str(e)}"
                        )
                        # Set running flag to False to indicate task failure
                        self.is_running = False
                        break
                    else:
                        # Wait a bit before retrying
                        self.logger.info(f"Waiting 30 seconds before retry...")
                        if self.stop_event.wait(timeout=30):
                            break

        finally:
            # Ensure cleanup happens regardless of how the loop exits
            self._cleanup_after_monitoring_loop()

        self.logger.info(f"Monitoring loop ended for task {self.task_id}")

    def _cleanup_after_monitoring_loop(self):
        """Clean up resources after monitoring loop ends"""
        try:
            # Close browser tab if still open
            if self.page:
                self.browser_manager.close_task_tab(self.task_id)
                self.page = None

            # Notify execution engine that this task has ended
            # This will be called by the execution engine to remove from tracking
            if hasattr(self, '_execution_engine_callback'):
                self._execution_engine_callback(self.task_id)

        except Exception as e:
            self.logger.error(f"Error during monitoring loop cleanup: {e}")
    
    def _perform_monitoring_cycle(self, task):
        """Perform a single monitoring cycle"""
        if not self.page:
            raise Exception("No browser page available")

        self.logger.debug(f"Performing monitoring cycle for task {self.task_id}")

        # 更新标签页使用时间，防止被清理
        active_page = self.browser_manager.get_task_tab(self.task_id)
        if not active_page:
            raise Exception("Task tab was closed or is no longer available")

        try:
            # 检查页面连接状态
            current_url = active_page.url
            self.logger.debug(f"Current page URL: {current_url}")

            # 刷新页面获取最新评论
            active_page.refresh()
            time.sleep(self.config.page_load_delay)

            # 更新页面引用
            self.page = active_page

        except Exception as e:
            self.logger.warning(f"Page refresh failed, attempting to reconnect: {e}")
            # 尝试重新导航到页面
            try:
                active_page.get(task.post_url)
                time.sleep(self.config.page_load_delay)
                self.page = active_page
            except Exception as reconnect_error:
                self.logger.error(f"Failed to reconnect to page: {reconnect_error}")
                raise
        
        # Get current comment count
        current_comment_count, comments = self._get_comment_count()

        # Check if there are new comments
        if current_comment_count > self.last_comment_count:
            self.logger.info(
                f"Task {self.task_id}: New comments detected "
                f"({self.last_comment_count} -> {current_comment_count})"
            )

            # Process new comments
            new_flash_sales = self._process_new_comments(
                self.current_page_url or task.post_url, current_comment_count,comments
            )
            
            if new_flash_sales:
                self.total_flash_sales_found += len(new_flash_sales)
                self.logger.info(
                    f"Task {self.task_id}: Found {len(new_flash_sales)} new flash sales"
                )
            
            self.last_comment_count = current_comment_count
        
        # 翻页器
        self._check_and_navigate_to_next_page(current_comment_count)

        # Update task statistics
        self.task_manager.update_task_stats(
            self.task_id, current_comment_count, self.total_flash_sales_found
        )
        
        # Save task state
        self._save_task_state(task.post_url, current_comment_count)
    
    def _get_comment_count(self):
        """Get the current comment count from the page using multi-forum extractor"""
        try:
            # 使用多论坛评论提取器
            if not hasattr(self, 'comment_extractor'):
                self.comment_extractor = MultiForumCommentExtractor()

            # 获取当前任务的 URL
            task = self.task_manager.get_task(self.task_id)
            if not task:
                self.logger.warning("Task not found, using last known count")
                return self.last_comment_count, []

            # 提取评论数量和内容
            comment_count, comments = self.comment_extractor.get_comment_count_and_content(
                self.page, task.post_url
            )

            self.logger.debug(f"提取到 {comment_count} 条评论")

            # 记录一些评论内容用于调试
            if comments and len(comments) > 0:
                self.logger.debug(f"最新评论预览: {comments[-1]['content'][:50]}...")

            return comment_count,comments

        except Exception as e:
            self.logger.warning(f"Failed to get comment count: {e}")
            return self.last_comment_count,[]
    
    def _process_new_comments(self, post_url: str, current_comment_count: int,comments:list) -> list:
        """Process new comments and detect flash sales"""
        flash_sales_found = []
        
        try:
            # Calculate how many new comments to process
            new_comment_count = current_comment_count - self.last_comment_count
            self.logger.info(f"Processing {new_comment_count} new comments for task {self.task_id}")
            self.logger.info(f"Total comment elements on page: {len(comments)}")

            # Process only the newest comments (the difference)
            processed_count = 0
            comments_to_process = min(new_comment_count, len(comments))

            # Process from the newest comments (last elements in the list)
            for i in range(comments_to_process):
                comment_element = comments[-(i+1)]  # Start from the last (newest) comment
                try:
                    # Extract comment content
                    comment_content = self._extract_comment_content(comment_element)
                    self.logger.debug(f"Processing new comment {i+1}/{comments_to_process}: '{comment_content[:50]}...'")

                    processed_count += 1

                    try:
                        # Get current task to check email settings
                        current_task = self.task_manager.get_task(self.task_id)

                        if current_task.email_notifications_enabled:
                            self.logger.info(f"Email notifications enabled for task {self.task_id}")
                            self.logger.debug(f"Comment preview: {comment_content[:100]}...")

                            # 优先使用优化的邮件服务（异步，不阻塞主线程）
                            email_success = False
                            if OPTIMIZED_EMAIL_AVAILABLE:
                                try:
                                    self.logger.info(f"Using optimized email service for task {self.task_id}")
                                    # 异步发送，立即返回，不阻塞监控线程
                                    email_success = optimized_email_service.send_notification(
                                        task_id=self.task_id,
                                        post_url=post_url,
                                        comment_content=comment_content,
                                        comment_count=current_comment_count,
                                        is_flash_sale=False,
                                        flash_sale_keywords=None,
                                        task_test_mode=current_task.email_test_mode
                                    )
                                    if email_success:
                                        self.logger.info(f"Optimized email service task submitted for {self.task_id}")
                                    else:
                                        self.logger.warning(f"Optimized email service failed for task {self.task_id}")
                                except Exception as opt_error:
                                    self.logger.error(f"Optimized email service error: {opt_error}")
                                    email_success = False

                            # 备用：使用原始邮件服务（同步，可能阻塞）
                            if not email_success and REAL_EMAIL_AVAILABLE:
                                try:
                                    self.logger.info(f"Fallback to real email service for task {self.task_id}")
                                    email_success = real_email_service.send_comment_notification(
                                        task_id=self.task_id,
                                        post_url=post_url,
                                        comment_content=comment_content,
                                        comment_count=current_comment_count,
                                        is_flash_sale=False,
                                        flash_sale_keywords=None,
                                        task_test_mode=current_task.email_test_mode
                                    )
                                    if email_success:
                                        self.logger.info(f"Real email service succeeded for task {self.task_id}")
                                except Exception as real_error:
                                    self.logger.error(f"Real email service error: {real_error}")
                                    email_success = False

                            if not email_success:
                                self.logger.warning(f"All email services failed for task {self.task_id}")
                                self.logger.info(f"OPTIMIZED_EMAIL_AVAILABLE = {OPTIMIZED_EMAIL_AVAILABLE}, REAL_EMAIL_AVAILABLE = {REAL_EMAIL_AVAILABLE}")
                        else:
                            self.logger.info(f"Email notifications disabled for task {self.task_id}")

                    except Exception as email_error:
                        self.logger.error(f"Failed to send email notification: {email_error}")
                        import traceback
                        self.logger.error(f"Email error traceback: {traceback.format_exc()}")

                except Exception as e:
                    self.logger.warning(f"Error processing comment: {e}")
                    continue
            
            # Update last processed comment content and count
            if comments and processed_count > 0:
                self.last_processed_comment_content = self._extract_comment_content(
                    comments[-1]  # Last (newest) comment
                )
                self.logger.info(f"Updated last processed comment for task {self.task_id}: '{self.last_processed_comment_content[:50]}...'")

            self.logger.info(f"Processed {processed_count} new comments for task {self.task_id}")
        
        except Exception as e:
            self.logger.error(f"Error processing new comments: {e}")
        
        return flash_sales_found
    
    def _extract_comment_content(self, comment_element) -> str:
        """Extract text content from a comment element or comment data dict"""
        try:
            # 检查是否是字典格式的评论数据（从 multi_forum_comment_extractor 返回）
            if isinstance(comment_element, dict):
                # 如果是字典，直接返回内容
                return comment_element.get("content", "").strip()

            # 如果是页面元素对象，使用原有逻辑
            # Get current task to determine forum type
            task = self.task_manager.get_task(self.task_id)
            if not task:
                return comment_element.text.strip() if hasattr(comment_element, 'text') and comment_element else ""

            # Detect forum type and use appropriate extraction method
            forum_type = self.comment_extractor.detect_forum_type(task.post_url)

            if forum_type in self.comment_extractor.forum_configs:
                config = self.comment_extractor.forum_configs[forum_type]

                # Use forum-specific content selector if available
                if config.get("comment_content_selector"):
                    content_element = comment_element.ele(config["comment_content_selector"], timeout=2)
                    if content_element:
                        return content_element.text.strip()

                # Otherwise use the entire element text
                return comment_element.text.strip() if hasattr(comment_element, 'text') and comment_element else ""
            else:
                # Fallback to LowEndTalk method for unknown forums
                content_element = comment_element.ele('css:.Message', timeout=2)
                if content_element:
                    return content_element.text.strip()
                return comment_element.text.strip() if hasattr(comment_element, 'text') and comment_element else ""
        except Exception as e:
            self.logger.warning(f"Error extracting comment content: {e}")
            # 尝试不同的方式获取内容
            if isinstance(comment_element, dict):
                return comment_element.get("content", "")
            elif hasattr(comment_element, 'text'):
                return comment_element.text.strip()
            else:
                return str(comment_element) if comment_element else ""

    def _extract_flash_sale_keywords(self, comment_content: str) -> list:
        """Extract flash sale keywords from comment content"""
        flash_sale_keywords = [
            'sale', 'offer', 'discount', 'promo', 'deal', 'special', 'limited',
            'flash', 'urgent', 'hurry', 'expires', 'today only', 'last chance',
            'cheap', 'price', 'off', '%', 'coupon', 'code'
        ]

        found_keywords = []
        comment_lower = comment_content.lower()

        for keyword in flash_sale_keywords:
            if keyword in comment_lower:
                found_keywords.append(keyword)

        return found_keywords
    
    def _load_task_state(self, post_url: str):
        """Load task state from persistent storage"""
        try:
            state = self.state_manager.load_state()
            post_state = state.get("processed_posts", {}).get(post_url, {})

            # Get stored comment count, but if it's 0 or missing, get actual current count
            stored_count = post_state.get("last_comment_count", 0)

            if stored_count == 0:
                # This is a new task or the stored count is invalid
                # Get the actual current comment count from the page
                try:
                    current_count, _ = self._get_comment_count()
                    self.last_comment_count = current_count
                    self.logger.info(
                        f"Initialized task {self.task_id} with actual current comment count: {current_count}"
                    )
                except Exception as e:
                    self.logger.warning(
                        f"Failed to get current comment count, using stored value: {e}"
                    )
                    self.last_comment_count = stored_count
            else:
                self.last_comment_count = stored_count
                self.logger.info(
                    f"Loaded existing state for task {self.task_id}: last_comment_count={self.last_comment_count}"
                )

            self.last_processed_comment_content = post_state.get("last_comment_content", "")
            
        except Exception as e:
            self.logger.warning(f"Failed to load task state: {e}")
    
    def _save_task_state(self, post_url: str, comment_count: int):
        """Save task state to persistent storage"""
        try:
            state = self.state_manager.load_state()
            
            if "processed_posts" not in state:
                state["processed_posts"] = {}
            
            state["processed_posts"][post_url] = {
                "last_comment_count": comment_count,
                "last_comment_content": self.last_processed_comment_content,
                "last_updated": time.time(),
                "task_id": self.task_id
            }
            
            self.state_manager.save_state(state)
            
        except Exception as e:
            self.logger.warning(f"Failed to save task state: {e}")


class TaskExecutionEngine:
    """
    Main execution engine that manages multiple crawling tasks
    """

    def __init__(self, config: CrawlerConfig, task_manager):
        self.config = config
        self.task_manager = task_manager
        self.browser_manager = TaskBrowserManager(config)
        self.logger = logging.getLogger("task_execution_engine")

        # Active task executors
        self.executors: Dict[str, TaskExecutor] = {}
        self.lock = threading.Lock()

        # Auto-restart configuration - 默认启用，不可禁用
        self.auto_restart_enabled = True  # 始终启用自动重启
        self.auto_restart_delay = 20      # 减少延迟到20秒，更快地恢复任务
        self.max_restart_attempts = 3     # 最多尝试3次
        self.restart_attempts: Dict[str, int] = {}  # task_id -> attempt_count

        # Start auto-restart monitor thread
        self.auto_restart_thread = threading.Thread(
            target=self._auto_restart_monitor,
            daemon=True,
            name="AutoRestartMonitor"
        )
        self.auto_restart_thread.start()

        self.logger.info("TaskExecutionEngine initialized with permanent auto-restart enabled")
    
    async def start_task(self, task_id: str):
        """Start executing a specific task"""
        with self.lock:
            if task_id in self.executors:
                self.logger.warning(f"Task {task_id} is already running")
                return

            # Create task executor
            executor = TaskExecutor(
                task_id, self.config, self.browser_manager, self.task_manager
            )

            # Set callback for task completion/failure
            executor._execution_engine_callback = self._on_task_ended

            self.executors[task_id] = executor

        # Start the executor with proper error handling
        try:
            await executor.start()
        except Exception as e:
            # If start fails, remove executor from tracking
            with self.lock:
                if task_id in self.executors:
                    del self.executors[task_id]

            self.logger.error(f"Failed to start task {task_id}, removed from tracking: {e}")
            raise
    
    async def stop_task(self, task_id: str):
        """Stop executing a specific task"""
        with self.lock:
            executor = self.executors.get(task_id)
            if not executor:
                self.logger.warning(f"Task {task_id} is not running in execution engine")
                # 即使任务不在执行器中，也更新数据库状态为stopped
                # 这解决了数据库状态与实际运行状态不一致的问题
                self.task_manager.update_task_status(task_id, "stopped")
                return

        # Stop the executor
        try:
            await executor.stop()
        except Exception as e:
            self.logger.error(f"Error stopping task {task_id}: {e}")
            # 确保即使停止过程出错，也更新状态
            self.task_manager.update_task_status(task_id, "stopped")

        with self.lock:
            # 使用安全的方式从字典中删除
            self.executors.pop(task_id, None)
    
    async def stop_all_tasks(self):
        """Stop all running tasks"""
        with self.lock:
            task_ids = list(self.executors.keys())

        self.logger.info(f"Stopping {len(task_ids)} running tasks")

        # Stop all executors
        for task_id in task_ids:
            try:
                await self.stop_task(task_id)
            except Exception as e:
                self.logger.error(f"Error stopping task {task_id}: {e}")
                # Force remove from executors if stop fails
                with self.lock:
                    if task_id in self.executors:
                        del self.executors[task_id]
                # Force update task status
                self.task_manager.update_task_status(task_id, "stopped")

        # Verify all tasks are properly cleaned up
        with self.lock:
            if self.executors:
                self.logger.warning(f"Some executors remain after stop_all_tasks: {list(self.executors.keys())}")
                self.executors.clear()

        # Reset browser connections to ensure clean state
        self.browser_manager.cleanup_all()
    
    def get_running_tasks(self) -> Set[str]:
        """Get set of currently running task IDs"""
        with self.lock:
            return set(self.executors.keys())
    
    def _on_task_ended(self, task_id: str):
        """Callback when a task monitoring loop ends"""
        with self.lock:
            if task_id in self.executors:
                self.logger.info(f"Task {task_id} monitoring loop ended, removing from tracking")
                del self.executors[task_id]

    def _auto_restart_monitor(self):
        """Background thread that monitors for failed tasks and attempts to restart them"""
        self.logger.info("Auto-restart monitor started")

        while True:
            try:
                # Auto-restart is always enabled now

                # Get all tasks with error status
                all_tasks = self.task_manager.get_all_tasks()
                error_tasks = [task for task in all_tasks if task.status == "error"]

                # Also check for stale running tasks
                running_tasks = [task for task in all_tasks if task.status == "running"]

                # First process error tasks
                for task in error_tasks:
                    task_id = task.id

                    # Check if task is not currently running
                    with self.lock:
                        if task_id in self.executors:
                            continue  # Task is still running, skip

                    # Check restart attempts
                    current_attempts = self.restart_attempts.get(task_id, 0)
                    if current_attempts >= self.max_restart_attempts:
                        # Reset attempts after a longer delay (3x normal delay)
                        if task.stopped_at:
                            try:
                                from datetime import datetime, timezone
                                stopped_at_str = str(task.stopped_at)
                                if stopped_at_str.endswith('Z'):
                                    stopped_time = datetime.fromisoformat(stopped_at_str.replace('Z', '+00:00'))
                                elif '+' in stopped_at_str:
                                    stopped_time = datetime.fromisoformat(stopped_at_str)
                                else:
                                    stopped_time = datetime.fromisoformat(stopped_at_str).replace(tzinfo=timezone.utc)

                                time_since_error = (datetime.now(timezone.utc) - stopped_time).total_seconds()

                                if time_since_error > self.auto_restart_delay * 3:
                                    # Reset attempts after extended delay
                                    self.logger.info(f"Resetting restart attempts for task {task_id} after extended delay")
                                    self.restart_attempts[task_id] = 0
                                    current_attempts = 0
                                else:
                                    continue  # Not enough time passed for reset
                            except Exception as e:
                                self.logger.warning(f"Error checking extended delay for task {task_id}: {e}")
                                continue
                        else:
                            continue  # No stopped_at timestamp, skip

                    # Check if enough time has passed since last error
                    if task.stopped_at:
                        try:
                            from datetime import datetime, timezone
                            # Handle different timestamp formats
                            stopped_at_str = str(task.stopped_at)
                            if stopped_at_str.endswith('Z'):
                                stopped_time = datetime.fromisoformat(stopped_at_str.replace('Z', '+00:00'))
                            elif '+' in stopped_at_str:
                                stopped_time = datetime.fromisoformat(stopped_at_str)
                            else:
                                # Assume UTC if no timezone info
                                stopped_time = datetime.fromisoformat(stopped_at_str).replace(tzinfo=timezone.utc)

                            time_since_error = (datetime.now(timezone.utc) - stopped_time).total_seconds()

                            if time_since_error < self.auto_restart_delay:
                                continue  # Not enough time passed
                        except Exception as e:
                            self.logger.warning(f"Error parsing stopped_at time for task {task_id}: {e}")
                            # Continue with restart attempt if time parsing fails

                    # Attempt to restart the task
                    self.logger.info(f"Auto-restarting failed task {task_id} (attempt {current_attempts + 1}/{self.max_restart_attempts})")

                    try:
                        # Update restart attempts counter
                        self.restart_attempts[task_id] = current_attempts + 1

                        # Reset task status to pending
                        self.task_manager.update_task_status(task_id, "pending")

                        # Start the task
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.start_task(task_id))
                        loop.close()

                        self.logger.info(f"Successfully auto-restarted task {task_id}")

                    except Exception as e:
                        self.logger.error(f"Failed to auto-restart task {task_id}: {e}")
                        # Update task status back to error with restart info
                        error_msg = f"Auto-restart failed (attempt {current_attempts + 1}): {str(e)}"
                        self.task_manager.update_task_status(task_id, "error", error_msg)

                # Check for stale running tasks (tasks marked as running but not in executors)
                for task in running_tasks:
                    task_id = task.id

                    with self.lock:
                        if task_id not in self.executors:
                            # Task is marked as running but not actually running
                            self.logger.warning(f"Detected stale running task {task_id}, updating status to error")
                            self.task_manager.update_task_status(
                                task_id, "error", "Task was marked as running but not actually executing"
                            )

                # Sleep before next check
                time.sleep(20)  # Check every 20 seconds for faster recovery

            except Exception as e:
                self.logger.error(f"Error in auto-restart monitor: {e}")
                time.sleep(60)  # Wait longer on error

    def reset_restart_attempts(self, task_id: str):
        """Reset restart attempts counter for a task (internal use only)"""
        with self.lock:
            if task_id in self.restart_attempts:
                del self.restart_attempts[task_id]
                self.logger.info(f"Reset restart attempts for task {task_id}")

    def cleanup(self):
        """Clean up all resources"""
        # Auto-restart remains enabled even during cleanup
        # Only stop the monitor thread when the engine is being destroyed
        self.browser_manager.cleanup_all()
        self.logger.info("TaskExecutionEngine cleanup complete")
