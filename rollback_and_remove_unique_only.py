#!/usr/bin/env python3
"""
回滚数据库并只删除 post_url 的唯一索引
保持原始数据不变
"""

import sqlite3
import os
import shutil
from datetime import datetime


def find_backup_file():
    """查找最新的备份文件"""
    backup_files = []
    for file in os.listdir('.'):
        if file.startswith('crawling_tasks.db.backup_'):
            backup_files.append(file)
    
    if backup_files:
        # 按时间戳排序，获取最新的备份
        backup_files.sort(reverse=True)
        return backup_files[0]
    
    return None


def rollback_database():
    """回滚数据库到备份版本"""
    print("=== 回滚数据库 ===\n")
    
    backup_file = find_backup_file()
    if not backup_file:
        print("❌ 未找到备份文件")
        return False
    
    print(f"找到备份文件: {backup_file}")
    
    try:
        # 备份当前的错误版本
        error_backup = f"crawling_tasks.db.error_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        if os.path.exists("crawling_tasks.db"):
            shutil.copy2("crawling_tasks.db", error_backup)
            print(f"当前错误版本已备份到: {error_backup}")
        
        # 恢复原始备份
        shutil.copy2(backup_file, "crawling_tasks.db")
        print(f"✅ 数据库已从 {backup_file} 恢复")
        
        return True
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False


def check_original_data():
    """检查原始数据"""
    print("=== 检查原始数据 ===\n")
    
    try:
        with sqlite3.connect("crawling_tasks.db") as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tasks'")
            schema = cursor.fetchone()[0]
            print("原始表结构:")
            print(schema)
            print()
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM tasks")
            count = cursor.fetchone()[0]
            print(f"任务数量: {count}")
            
            # 检查几个任务的原始数据
            cursor.execute("SELECT id, email_notifications_enabled, email_test_mode, status, comment_count FROM tasks LIMIT 3")
            tasks = cursor.fetchall()
            
            print("\n前3个任务的原始数据:")
            for task in tasks:
                print(f"  ID: {task[0][:8]}...")
                print(f"    email_notifications_enabled: {task[1]} ({type(task[1]).__name__})")
                print(f"    email_test_mode: {task[2]} ({type(task[2]).__name__})")
                print(f"    status: {task[3]} ({type(task[3]).__name__})")
                print(f"    comment_count: {task[4]} ({type(task[4]).__name__})")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 检查原始数据失败: {e}")
        return False


def remove_unique_constraint_only():
    """只删除 post_url 的唯一约束，保持数据不变"""
    print("=== 删除 post_url 唯一约束 ===\n")
    
    try:
        with sqlite3.connect("crawling_tasks.db") as conn:
            cursor = conn.cursor()
            
            # 1. 获取所有数据
            cursor.execute("SELECT * FROM tasks")
            all_data = cursor.fetchall()
            
            # 2. 获取列信息
            cursor.execute("PRAGMA table_info(tasks)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            print(f"准备迁移 {len(all_data)} 条记录")
            print(f"列: {column_names}")
            
            # 3. 重命名原表
            cursor.execute("ALTER TABLE tasks RENAME TO tasks_old")
            
            # 4. 创建新表（没有 UNIQUE 约束）
            cursor.execute("""
                CREATE TABLE tasks (
                    id TEXT PRIMARY KEY,
                    post_url TEXT NOT NULL,
                    forum_domain TEXT NOT NULL,
                    monitor_interval INTEGER NOT NULL,
                    ai_analysis_enabled BOOLEAN NOT NULL,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    stopped_at TEXT,
                    last_check_at TEXT,
                    comment_count INTEGER DEFAULT 0,
                    flash_sales_found INTEGER DEFAULT 0,
                    error_message TEXT,
                    email_notifications_enabled BOOLEAN DEFAULT 1,
                    email_test_mode BOOLEAN DEFAULT 1
                )
            """)
            
            # 5. 迁移所有原始数据（保持原样）
            placeholders = ','.join(['?' for _ in column_names])
            cursor.executemany(f"INSERT INTO tasks VALUES ({placeholders})", all_data)
            
            # 6. 删除旧表
            cursor.execute("DROP TABLE tasks_old")
            
            conn.commit()
            
            print("✅ 唯一约束删除成功，原始数据保持不变")
            
            # 7. 验证新表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tasks'")
            new_schema = cursor.fetchone()[0]
            print("\n新表结构:")
            print(new_schema)
            
            # 8. 验证数据完整性
            cursor.execute("SELECT COUNT(*) FROM tasks")
            new_count = cursor.fetchone()[0]
            print(f"\n数据验证: {new_count} 条记录")
            
            if new_count == len(all_data):
                print("✅ 数据完整性验证通过")
                return True
            else:
                print("❌ 数据完整性验证失败")
                return False
            
    except Exception as e:
        print(f"❌ 删除唯一约束失败: {e}")
        return False


def test_duplicate_url():
    """测试是否可以插入重复URL"""
    print("\n=== 测试重复URL插入 ===\n")
    
    try:
        with sqlite3.connect("crawling_tasks.db") as conn:
            cursor = conn.cursor()
            
            test_url = "https://test.com/duplicate-test"
            
            # 尝试插入两个相同URL的任务
            cursor.execute("""
                INSERT INTO tasks (id, post_url, forum_domain, monitor_interval, ai_analysis_enabled, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, ("test1", test_url, "test.com", 60, 1, "pending", "2024-01-01T00:00:00"))
            
            cursor.execute("""
                INSERT INTO tasks (id, post_url, forum_domain, monitor_interval, ai_analysis_enabled, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, ("test2", test_url, "test.com", 60, 1, "pending", "2024-01-01T00:00:00"))
            
            print("✅ 重复URL插入测试成功")
            
            # 清理测试数据
            cursor.execute("DELETE FROM tasks WHERE id IN ('test1', 'test2')")
            conn.commit()
            
            return True
            
    except sqlite3.IntegrityError as e:
        print(f"❌ 重复URL插入测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False


def main():
    """主函数"""
    print("数据库回滚并删除唯一约束\n")
    
    # 1. 回滚数据库
    if not rollback_database():
        print("❌ 回滚失败，退出")
        return
    
    # 2. 检查原始数据
    if not check_original_data():
        print("❌ 原始数据检查失败")
        return
    
    # 3. 只删除唯一约束
    if not remove_unique_constraint_only():
        print("❌ 删除唯一约束失败")
        return
    
    # 4. 测试重复URL
    if test_duplicate_url():
        print("\n🎉 操作完成！")
        print("✅ 数据库已回滚到原始状态")
        print("✅ post_url 唯一约束已删除")
        print("✅ 原始数据完全保持不变")
        print("✅ 现在可以正常进行翻页URL更新")
    else:
        print("\n⚠️  唯一约束可能仍然存在")


if __name__ == "__main__":
    main()
