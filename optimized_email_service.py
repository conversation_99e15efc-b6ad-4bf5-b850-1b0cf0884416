#!/usr/bin/env python3
"""
优化的邮件服务 - 专为任务执行引擎设计
支持异步发送、连接池管理、超时控制
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
import threading
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
import queue
import concurrent.futures
from contextlib import contextmanager


@dataclass
class OptimizedEmailConfig:
    """优化的邮件配置"""
    smtp_server: str = "smtp.qq.com"
    smtp_port: int = 465
    username: str = "<EMAIL>"
    password: str = "majvjpnlzjahfgei"
    recipient: str = "<EMAIL>"
    enabled: bool = True
    test_mode: bool = False
    
    # 连接管理
    connection_timeout: int = 10  # 连接超时
    send_timeout: int = 30  # 发送超时
    connection_lifetime: int = 300  # 连接生命周期（5分钟）
    max_connections: int = 2
    
    # 异步发送
    async_enabled: bool = True
    max_workers: int = 2
    queue_timeout: int = 5  # 队列超时


class OptimizedSMTPConnection:
    """优化的SMTP连接"""
    
    def __init__(self, config: OptimizedEmailConfig):
        self.config = config
        self.server = None
        self.created_at = time.time()
        self.last_used = self.created_at
        self.is_valid = False
        self.lock = threading.Lock()
        self._connect()
    
    def _connect(self):
        """建立SMTP连接"""
        try:
            self.server = smtplib.SMTP_SSL(
                self.config.smtp_server,
                self.config.smtp_port,
                timeout=self.config.connection_timeout
            )
            self.server.login(self.config.username, self.config.password)
            self.is_valid = True
        except Exception as e:
            self.is_valid = False
            raise e
    
    def is_expired(self) -> bool:
        """检查连接是否过期"""
        return time.time() - self.created_at > self.config.connection_lifetime
    
    def is_healthy(self) -> bool:
        """检查连接健康状态"""
        if not self.is_valid or self.is_expired():
            return False
        
        try:
            with self.lock:
                self.server.noop()
            return True
        except Exception:
            self.is_valid = False
            return False
    
    def send_email(self, msg: MIMEMultipart) -> bool:
        """发送邮件"""
        if not self.is_healthy():
            return False
        
        try:
            with self.lock:
                self.server.sendmail(
                    self.config.username,
                    self.config.recipient,
                    msg.as_string()
                )
                self.last_used = time.time()
            return True
        except Exception:
            self.is_valid = False
            return False
    
    def close(self):
        """关闭连接"""
        try:
            with self.lock:
                if self.server and self.is_valid:
                    self.server.quit()
        except Exception:
            pass
        finally:
            self.is_valid = False


class OptimizedEmailService:
    """优化的邮件服务"""
    
    def __init__(self, config: OptimizedEmailConfig = None):
        self.config = config or OptimizedEmailConfig()
        self.logger = self._setup_logger()
        
        # 连接池
        self.connections = queue.Queue(maxsize=self.config.max_connections)
        self.connection_lock = threading.Lock()
        
        # 异步发送
        self.executor = None
        self.is_running = False
        
        # 重复检测
        self.sent_notifications: Dict[str, Set[str]] = {}
        self.notification_lock = threading.Lock()
        
        # 启动服务
        if self.config.async_enabled:
            self._start_async_service()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("optimized_email")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _start_async_service(self):
        """启动异步服务"""
        if self.is_running:
            return
        
        self.is_running = True
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config.max_workers,
            thread_name_prefix="OptimizedEmail"
        )
        self.logger.info("异步邮件服务已启动")
    
    def _get_connection(self) -> Optional[OptimizedSMTPConnection]:
        """获取可用连接"""
        # 尝试从池中获取
        try:
            connection = self.connections.get_nowait()
            if connection.is_healthy():
                return connection
            else:
                connection.close()
        except queue.Empty:
            pass
        
        # 创建新连接
        try:
            return OptimizedSMTPConnection(self.config)
        except Exception as e:
            self.logger.error(f"创建SMTP连接失败: {e}")
            return None
    
    def _return_connection(self, connection: OptimizedSMTPConnection):
        """归还连接"""
        if connection.is_healthy():
            try:
                self.connections.put_nowait(connection)
            except queue.Full:
                connection.close()
        else:
            connection.close()
    
    def send_notification(self, task_id: str, post_url: str, comment_content: str,
                         comment_count: int, is_flash_sale: bool = False,
                         flash_sale_keywords: List[str] = None,
                         task_test_mode: bool = None) -> bool:
        """发送邮件通知 - 主入口"""
        
        if not self.config.enabled:
            return False
        
        # 检查重复
        if self._is_duplicate(task_id, post_url, comment_content):
            return True
        
        # 创建邮件内容
        try:
            msg = self._create_email(
                post_url, comment_content, comment_count,
                is_flash_sale, flash_sale_keywords or [],
                task_test_mode if task_test_mode is not None else self.config.test_mode
            )
        except Exception as e:
            self.logger.error(f"创建邮件失败: {e}")
            return False
        
        if self.config.async_enabled and self.is_running:
            # 异步发送
            try:
                future = self.executor.submit(self._send_email_sync, msg, task_id)
                # 不等待结果，立即返回
                self.logger.debug(f"邮件任务已提交到异步队列: {task_id}")
                return True
            except Exception as e:
                self.logger.error(f"提交异步任务失败: {e}")
                self._remove_duplicate_marker(task_id, post_url, comment_content)
                return False
        else:
            # 同步发送
            return self._send_email_sync(msg, task_id)
    
    def _send_email_sync(self, msg: MIMEMultipart, task_id: str) -> bool:
        """同步发送邮件"""
        connection = self._get_connection()
        if not connection:
            return False
        
        try:
            success = connection.send_email(msg)
            if success:
                self.logger.info(f"邮件发送成功: {task_id}")
            return success
        finally:
            self._return_connection(connection)
    
    def _is_duplicate(self, task_id: str, post_url: str, comment_content: str) -> bool:
        """检查重复发送"""
        comment_hash = hashlib.md5(
            f"{task_id}:{post_url}:{comment_content}".encode('utf-8')
        ).hexdigest()
        
        with self.notification_lock:
            if task_id not in self.sent_notifications:
                self.sent_notifications[task_id] = set()
            
            if comment_hash in self.sent_notifications[task_id]:
                return True
            
            self.sent_notifications[task_id].add(comment_hash)
            return False
    
    def _remove_duplicate_marker(self, task_id: str, post_url: str, comment_content: str):
        """移除重复标记"""
        comment_hash = hashlib.md5(
            f"{task_id}:{post_url}:{comment_content}".encode('utf-8')
        ).hexdigest()
        
        with self.notification_lock:
            if task_id in self.sent_notifications:
                self.sent_notifications[task_id].discard(comment_hash)
    
    def _create_email(self, post_url: str, comment_content: str, comment_count: int,
                     is_flash_sale: bool, flash_sale_keywords: List[str], test_mode: bool) -> MIMEMultipart:
        """创建邮件"""
        msg = MIMEMultipart()
        msg['From'] = self.config.username
        msg['To'] = self.config.recipient
        
        # 主题
        if is_flash_sale:
            subject = "🚨 NodeSeek闪购提醒: 检测到新的闪购信息"
        else:
            subject = "📢 NodeSeek论坛监控: 检测到新评论"
        
        if test_mode:
            subject += " (测试模式)"
        
        msg['Subject'] = subject
        
        # 正文
        safe_comment = comment_content[:200] if comment_content else "无内容"
        body = f"""
NodeSeek论坛监控系统检测到新的{'闪购信息' if is_flash_sale else '评论'}:

帖子链接: {post_url}
评论内容: {safe_comment}
当前评论总数: {comment_count}
闪购检测: {'是' if is_flash_sale else '否'}
"""
        
        if is_flash_sale and flash_sale_keywords:
            body += f"闪购关键词: {', '.join(flash_sale_keywords)}\n"
        
        if test_mode:
            body += "\n[测试模式] 这是一个测试通知。\n"
        
        body += f"""
检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

--
NodeSeek论坛监控系统
优化版邮件服务
"""
        
        msg.attach(MIMEText(body, 'plain'))
        return msg
    
    def cleanup(self):
        """清理资源"""
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None
        
        # 清理连接池
        while True:
            try:
                connection = self.connections.get_nowait()
                connection.close()
            except queue.Empty:
                break
        
        self.is_running = False
        self.logger.info("邮件服务已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


# 全局实例
optimized_email_service = OptimizedEmailService()
