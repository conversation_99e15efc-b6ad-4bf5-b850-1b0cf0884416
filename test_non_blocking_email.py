#!/usr/bin/env python3
"""
测试邮件发送不阻塞主线程
模拟任务执行引擎的监控循环
"""

import time
import threading
from optimized_email_service import OptimizedEmailService, OptimizedEmailConfig


class MockTaskMonitor:
    """模拟任务监控器"""
    
    def __init__(self):
        self.config = OptimizedEmailConfig(
            async_enabled=True,
            max_workers=2,
            test_mode=True
        )
        self.email_service = OptimizedEmailService(self.config)
        self.is_running = False
        self.monitor_count = 0
        
    def start_monitoring(self):
        """开始监控"""
        self.is_running = True
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        self.email_service.cleanup()
    
    def _monitoring_loop(self):
        """监控循环 - 模拟任务执行引擎的主循环"""
        print("开始监控循环...")
        
        while self.is_running:
            self.monitor_count += 1
            cycle_start = time.time()
            
            print(f"[监控循环 {self.monitor_count}] 开始检查...")
            
            # 模拟检查评论
            self._check_comments()
            
            # 模拟其他监控任务
            self._perform_other_tasks()
            
            cycle_end = time.time()
            cycle_time = cycle_end - cycle_start
            
            print(f"[监控循环 {self.monitor_count}] 完成，耗时: {cycle_time:.3f}秒")
            
            # 监控间隔
            time.sleep(2)
    
    def _check_comments(self):
        """模拟检查评论并发送邮件"""
        # 模拟发现新评论
        if self.monitor_count % 3 == 1:  # 每3个循环发现一次新评论
            print(f"  发现新评论，发送邮件通知...")
            
            email_start = time.time()
            
            # 发送邮件 - 这里应该不阻塞
            success = self.email_service.send_notification(
                task_id=f"monitor-test-{self.monitor_count}",
                post_url="https://www.nodeseek.com/post-394729-1",
                comment_content=f"监控循环 {self.monitor_count} 发现的新评论",
                comment_count=self.monitor_count,
                is_flash_sale=(self.monitor_count % 5 == 0),
                task_test_mode=True
            )
            
            email_end = time.time()
            email_time = email_end - email_start
            
            print(f"  邮件发送{'成功' if success else '失败'}，耗时: {email_time:.3f}秒")
            
            # 如果邮件发送耗时超过1秒，说明可能阻塞了
            if email_time > 1.0:
                print(f"  ⚠️  警告: 邮件发送耗时过长 ({email_time:.3f}秒)，可能阻塞了主线程!")
            else:
                print(f"  ✅ 邮件发送快速完成，主线程未被阻塞")
    
    def _perform_other_tasks(self):
        """模拟其他监控任务"""
        # 模拟页面刷新
        time.sleep(0.1)
        
        # 模拟数据处理
        time.sleep(0.05)
        
        # 模拟状态更新
        time.sleep(0.02)


def test_blocking_comparison():
    """对比测试：阻塞 vs 非阻塞"""
    print("=== 对比测试：阻塞 vs 非阻塞邮件发送 ===\n")
    
    # 测试1: 非阻塞异步发送
    print("1. 测试异步非阻塞发送:")
    async_config = OptimizedEmailConfig(async_enabled=True, test_mode=True)
    async_service = OptimizedEmailService(async_config)
    
    start_time = time.time()
    for i in range(3):
        success = async_service.send_notification(
            task_id=f"async-{i}",
            post_url="https://www.nodeseek.com/post-394729-1",
            comment_content=f"异步测试邮件 {i+1}",
            comment_count=i+1,
            is_flash_sale=False,
            task_test_mode=True
        )
        print(f"  异步邮件 {i+1}: {'提交成功' if success else '提交失败'}")
    
    async_time = time.time() - start_time
    print(f"  异步发送3封邮件耗时: {async_time:.3f}秒\n")
    
    # 测试2: 阻塞同步发送
    print("2. 测试同步阻塞发送:")
    sync_config = OptimizedEmailConfig(async_enabled=False, test_mode=True)
    sync_service = OptimizedEmailService(sync_config)
    
    start_time = time.time()
    for i in range(3):
        success = sync_service.send_notification(
            task_id=f"sync-{i}",
            post_url="https://www.nodeseek.com/post-394729-1",
            comment_content=f"同步测试邮件 {i+1}",
            comment_count=i+1,
            is_flash_sale=False,
            task_test_mode=True
        )
        print(f"  同步邮件 {i+1}: {'发送成功' if success else '发送失败'}")
    
    sync_time = time.time() - start_time
    print(f"  同步发送3封邮件耗时: {sync_time:.3f}秒\n")
    
    # 对比结果
    print("3. 对比结果:")
    print(f"  异步发送耗时: {async_time:.3f}秒")
    print(f"  同步发送耗时: {sync_time:.3f}秒")
    print(f"  性能提升: {(sync_time/async_time):.1f}倍")
    print(f"  主线程阻塞时间减少: {(sync_time-async_time):.3f}秒\n")
    
    # 清理
    async_service.cleanup()
    sync_service.cleanup()


def main():
    """主测试函数"""
    print("测试邮件发送不阻塞主线程\n")
    
    # 对比测试
    test_blocking_comparison()
    
    # 模拟监控循环测试
    print("=== 模拟监控循环测试 ===")
    monitor = MockTaskMonitor()
    
    try:
        # 启动监控
        monitor_thread = monitor.start_monitoring()
        
        # 运行15秒
        print("监控将运行15秒，观察主线程是否被邮件发送阻塞...\n")
        time.sleep(15)
        
        # 停止监控
        print("\n停止监控...")
        monitor.stop_monitoring()
        
        # 等待监控线程结束
        monitor_thread.join(timeout=2)
        
        print(f"监控完成，总共执行了 {monitor.monitor_count} 个监控循环")
        print("如果每个循环耗时都很短，说明邮件发送没有阻塞主线程")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        monitor.stop_monitoring()


if __name__ == "__main__":
    main()
