"""
Task Management API for Web Crawling System
Provides REST API endpoints for managing forum post crawling tasks
"""

import sqlite3
import uuid
import threading
import time
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from enum import Enum

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from contextlib import asynccontextmanager
import uvicorn

try:
    from improved_forum_crawler import CrawlerConfig, ForumCrawler
except ImportError:
    print("Warning: improved_forum_crawler not found, using basic config")
    import json
    import os

    class CrawlerConfig:
        def __init__(self):
            self.flash_sale_keywords = ["offer", "sale", "discount"]
            self.base_url = "https://lowendtalk.com/"
            self.state_file = "task_system_state.json"
            self.results_file = "task_system_results.json"
            self.headless = True
            self.page_load_delay = 3.0
            self.redis_enabled = True
            self.redis_host = "localhost"
            self.redis_port = 6379
            self.redis_db = 0

        def load_from_file(self, path):
            """Load configuration from JSON file"""
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        config_data = json.load(f)
                        for key, value in config_data.items():
                            if hasattr(self, key):
                                setattr(self, key, value)
                except Exception as e:
                    print(f"Warning: Could not load config from {path}: {e}")

from task_execution_engine import TaskExecutionEngine
from email_notification_service import EmailNotificationService


class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running" 
    STOPPED = "stopped"
    COMPLETED = "completed"
    ERROR = "error"


class TaskBase(BaseModel):
    post_url: str = Field(..., description="URL of the forum post to crawl")
    forum_domain: str = Field(..., description="Domain of the forum (e.g., lowendtalk.com)")
    monitor_interval: int = Field(default=45, description="Monitoring interval in seconds")
    ai_analysis_enabled: bool = Field(default=True, description="Enable AI analysis for flash sales")
    email_notifications_enabled: bool = Field(default=True, description="Enable email notifications for this task")
    email_test_mode: bool = Field(default=True, description="Send emails for all comments (test) or only flash sales (production)")


class TaskCreate(TaskBase):
    pass


class TaskUpdate(BaseModel):
    monitor_interval: Optional[int] = None
    ai_analysis_enabled: Optional[bool] = None
    email_notifications_enabled: Optional[bool] = None
    email_test_mode: Optional[bool] = None


class TaskInDB(TaskBase):
    id: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None
    last_check_at: Optional[datetime] = None
    comment_count: int = 0
    flash_sales_found: int = 0
    error_message: Optional[str] = None


class TaskManager:
    """Manages task persistence and lifecycle"""
    
    def __init__(self, db_path: str = "crawling_tasks.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_db()
    
    def _init_db(self):
        """Initialize the tasks database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    post_url TEXT UNIQUE NOT NULL,
                    forum_domain TEXT NOT NULL,
                    monitor_interval INTEGER NOT NULL,
                    ai_analysis_enabled BOOLEAN NOT NULL,
                    email_notifications_enabled BOOLEAN DEFAULT 1,
                    email_test_mode BOOLEAN DEFAULT 1,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    stopped_at TEXT,
                    last_check_at TEXT,
                    comment_count INTEGER DEFAULT 0,
                    flash_sales_found INTEGER DEFAULT 0,
                    error_message TEXT
                )
            """)

            # Add email notification columns if they don't exist (migration)
            try:
                conn.execute("ALTER TABLE tasks ADD COLUMN email_notifications_enabled BOOLEAN DEFAULT 1")
                conn.execute("ALTER TABLE tasks ADD COLUMN email_test_mode BOOLEAN DEFAULT 1")
            except sqlite3.OperationalError:
                # Columns already exist
                pass

            conn.commit()
    
    def create_task(self, task_data: TaskCreate) -> TaskInDB:
        """Create a new crawling task"""
        with self.lock:
            task_id = str(uuid.uuid4())
            now = datetime.now(timezone.utc)
            
            with sqlite3.connect(self.db_path) as conn:
                try:
                    conn.execute("""
                        INSERT INTO tasks (
                            id, post_url, forum_domain, monitor_interval,
                            ai_analysis_enabled, email_notifications_enabled,
                            email_test_mode, status, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        task_id, task_data.post_url, task_data.forum_domain,
                        task_data.monitor_interval, task_data.ai_analysis_enabled,
                        task_data.email_notifications_enabled, task_data.email_test_mode,
                        TaskStatus.PENDING, now.isoformat()
                    ))
                    conn.commit()
                    
                    return self.get_task(task_id)
                    
                except sqlite3.IntegrityError:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Task for post URL {task_data.post_url} already exists"
                    )
    
    def get_task(self, task_id: str) -> TaskInDB:
        """Get a task by ID"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
            row = cursor.fetchone()
            
            if not row:
                raise HTTPException(status_code=404, detail="Task not found")
            
            return TaskInDB(**dict(row))
    
    def get_all_tasks(self) -> List[TaskInDB]:
        """Get all tasks"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM tasks ORDER BY created_at DESC")
            rows = cursor.fetchall()
            
            return [TaskInDB(**dict(row)) for row in rows]
    
    def update_task_status(self, task_id: str, status: TaskStatus, 
                          error_message: Optional[str] = None):
        """Update task status"""
        with self.lock:
            now = datetime.now(timezone.utc).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                if status == TaskStatus.RUNNING:
                    conn.execute("""
                        UPDATE tasks SET status = ?, started_at = ?, error_message = NULL 
                        WHERE id = ?
                    """, (status, now, task_id))
                elif status in [TaskStatus.STOPPED, TaskStatus.COMPLETED, TaskStatus.ERROR]:
                    conn.execute("""
                        UPDATE tasks SET status = ?, stopped_at = ?, error_message = ? 
                        WHERE id = ?
                    """, (status, now, error_message, task_id))
                else:
                    conn.execute("""
                        UPDATE tasks SET status = ?, error_message = ? WHERE id = ?
                    """, (status, error_message, task_id))
                
                conn.commit()
    
    def update_task_stats(self, task_id: str, comment_count: int, flash_sales_found: int):
        """Update task statistics"""
        with self.lock:
            now = datetime.now(timezone.utc).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE tasks SET comment_count = ?, flash_sales_found = ?, last_check_at = ?
                    WHERE id = ?
                """, (comment_count, flash_sales_found, now, task_id))
                conn.commit()

    def update_task_url(self, task_id: str, new_url: str):
        """Update task URL (for pagination)"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE tasks SET post_url = ? WHERE id = ?
                """, (new_url, task_id))
                conn.commit()

    def update_task(self, task_id: str, update_data: TaskUpdate):
        """Update task configuration"""
        with self.lock:
            # Build dynamic update query
            update_fields = []
            values = []

            if update_data.monitor_interval is not None:
                update_fields.append("monitor_interval = ?")
                values.append(update_data.monitor_interval)

            if update_data.ai_analysis_enabled is not None:
                update_fields.append("ai_analysis_enabled = ?")
                values.append(update_data.ai_analysis_enabled)

            if update_data.email_notifications_enabled is not None:
                update_fields.append("email_notifications_enabled = ?")
                values.append(update_data.email_notifications_enabled)

            if update_data.email_test_mode is not None:
                update_fields.append("email_test_mode = ?")
                values.append(update_data.email_test_mode)

            if not update_fields:
                return  # Nothing to update

            values.append(task_id)  # Add task_id for WHERE clause

            with sqlite3.connect(self.db_path) as conn:
                query = f"UPDATE tasks SET {', '.join(update_fields)} WHERE id = ?"
                conn.execute(query, values)
                conn.commit()

    def delete_task(self, task_id: str):
        """Delete a task"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
                if cursor.rowcount == 0:
                    raise HTTPException(status_code=404, detail="Task not found")
                conn.commit()
    
    def get_active_tasks(self) -> List[TaskInDB]:
        """Get all active (running) tasks"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                "SELECT * FROM tasks WHERE status = ? ORDER BY created_at", 
                (TaskStatus.RUNNING,)
            )
            rows = cursor.fetchall()
            
            return [TaskInDB(**dict(row)) for row in rows]


# Global instances
task_manager = TaskManager()
execution_engine = None  # Will be initialized in startup


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global execution_engine

    # Startup
    print("🚀 Starting Task Management API...")

    # Load crawler configuration
    config = CrawlerConfig()
    config.load_from_file("crawler_config.json")

    # Initialize execution engine
    execution_engine = TaskExecutionEngine(config, task_manager)

    print("🚀 Task Management API started successfully")

    yield

    # Shutdown
    print("🛑 Shutting down Task Management API...")

    if execution_engine:
        await execution_engine.stop_all_tasks()
        execution_engine.cleanup()

    print("🛑 Task Management API shutdown complete")


app = FastAPI(
    title="Web Crawling Task Management API",
    description="REST API for managing forum post crawling tasks with real-time monitoring",
    version="1.0.0",
    lifespan=lifespan
)


# API Endpoints

@app.get("/")
async def read_root():
    """API health check"""
    return {
        "message": "Web Crawling Task Management API",
        "status": "running",
        "active_tasks": len(task_manager.get_active_tasks())
    }


@app.post("/tasks", response_model=TaskInDB, summary="Create new crawling task")
async def create_task(task: TaskCreate):
    """Create a new forum post crawling task"""
    return task_manager.create_task(task)


@app.get("/tasks", response_model=List[TaskInDB], summary="List all tasks")
async def list_tasks():
    """Get all crawling tasks"""
    return task_manager.get_all_tasks()


@app.get("/tasks/{task_id}", response_model=TaskInDB, summary="Get specific task")
async def get_task(task_id: str):
    """Get a specific task by ID"""
    return task_manager.get_task(task_id)


@app.put("/tasks/{task_id}/start", summary="Start a specific task")
async def start_task(task_id: str, background_tasks: BackgroundTasks):
    """Start monitoring a specific crawling task"""
    task = task_manager.get_task(task_id)

    # Check if task is already running in execution engine
    running_tasks = execution_engine.get_running_tasks()
    if task_id in running_tasks:
        raise HTTPException(status_code=400, detail="Task is already running")

    # Handle different task statuses
    if task.status == TaskStatus.ERROR:
        # Always reset restart attempts counter for manual restart
        execution_engine.reset_restart_attempts(task_id)
        task_manager.update_task_status(task_id, TaskStatus.PENDING)
        message_suffix = " (restarting from error state)"
    elif task.status == TaskStatus.RUNNING:
        # Double check - this might be a stale status
        if task_id not in running_tasks:
            # Stale running status - reset and restart
            execution_engine.reset_restart_attempts(task_id)
            task_manager.update_task_status(task_id, TaskStatus.PENDING)
            message_suffix = " (corrected stale running status)"
        else:
            raise HTTPException(status_code=400, detail="Task is already running")
    else:
        # For any other status, reset restart attempts
        execution_engine.reset_restart_attempts(task_id)
        message_suffix = ""

    # Start task in background
    background_tasks.add_task(execution_engine.start_task, task_id)

    return {"message": f"Task {task_id} started successfully{message_suffix}"}


@app.put("/tasks/{task_id}/stop", summary="Stop a specific task")
async def stop_task(task_id: str):
    """Stop a specific crawling task"""
    task = task_manager.get_task(task_id)
    
    if task.status != TaskStatus.RUNNING:
        raise HTTPException(status_code=400, detail="Task is not running")
    
    await execution_engine.stop_task(task_id)
    
    return {"message": f"Task {task_id} stopped successfully"}


@app.delete("/tasks/{task_id}", summary="Delete a specific task")
async def delete_task(task_id: str):
    """Delete a crawling task"""
    task = task_manager.get_task(task_id)
    
    if task.status == TaskStatus.RUNNING:
        await execution_engine.stop_task(task_id)
    
    task_manager.delete_task(task_id)
    
    return {"message": f"Task {task_id} deleted successfully"}


@app.post("/tasks/stop-all", summary="Stop all running tasks")
async def stop_all_tasks():
    """Stop all currently running tasks and reset browser state"""
    active_tasks = task_manager.get_active_tasks()

    # Always perform cleanup, even if no active tasks
    await execution_engine.stop_all_tasks()

    # Force kill any remaining Chrome processes
    try:
        import psutil
        killed_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
            if 'chrome' in proc.info['name'].lower():
                proc.kill()
                killed_count += 1

        message = f"Stopped {len(active_tasks)} tasks successfully"
        if killed_count > 0:
            message += f" and killed {killed_count} Chrome processes"

        return {
            "message": message,
            "stopped_tasks": [task.id for task in active_tasks],
            "killed_processes": killed_count
        }
    except Exception as e:
        return {
            "message": f"Stopped {len(active_tasks)} tasks with cleanup errors: {str(e)}",
            "stopped_tasks": [task.id for task in active_tasks]
        }





@app.post("/system/reset-browsers", summary="Reset all browser instances")
async def reset_browsers():
    """Force reset all browser instances"""
    # 强制清理所有浏览器
    execution_engine.browser_manager.cleanup_all()

    # 尝试强制结束所有Chrome进程
    try:
        import psutil
        killed_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
            if 'chrome' in proc.info['name'].lower():
                proc.kill()
                killed_count += 1

        return {
            "message": "Browser reset completed successfully",
            "killed_processes": killed_count
        }
    except Exception as e:
        return {
            "message": f"Browser reset completed with errors: {str(e)}"
        }


# Email Notification Management Endpoints







@app.put("/tasks/{task_id}/email-settings", summary="Update email settings for a task")
async def update_task_email_settings(
    task_id: str,
    email_notifications_enabled: bool = None,
    email_test_mode: bool = None
):
    """Update email notification settings for a specific task"""
    task = task_manager.get_task(task_id)

    update_data = {}
    if email_notifications_enabled is not None:
        update_data["email_notifications_enabled"] = email_notifications_enabled
    if email_test_mode is not None:
        update_data["email_test_mode"] = email_test_mode

    if update_data:
        task_manager.update_task(task_id, TaskUpdate(**update_data))
        return {"message": f"Email settings updated for task {task_id}", "updates": update_data}
    else:
        return {"message": "No email settings to update"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
