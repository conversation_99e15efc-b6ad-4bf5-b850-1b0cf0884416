#!/usr/bin/env python3
"""
测试优化的邮件服务
验证异步发送、连接池管理、超时控制等功能
"""

import time
import threading
from optimized_email_service import OptimizedEmailService, OptimizedEmailConfig


def test_sync_sending():
    """测试同步发送"""
    print("=== 测试同步发送 ===")
    
    config = OptimizedEmailConfig(
        async_enabled=False,  # 禁用异步
        test_mode=True
    )
    
    service = OptimizedEmailService(config)
    
    try:
        success = service.send_notification(
            task_id="sync-test-001",
            post_url="https://www.nodeseek.com/post-394729-1",
            comment_content="这是同步发送测试邮件",
            comment_count=1,
            is_flash_sale=False,
            task_test_mode=True
        )
        
        print(f"同步发送结果: {'成功' if success else '失败'}")
        
    finally:
        service.cleanup()


def test_async_sending():
    """测试异步发送"""
    print("\n=== 测试异步发送 ===")
    
    config = OptimizedEmailConfig(
        async_enabled=True,  # 启用异步
        max_workers=2,
        test_mode=True
    )
    
    service = OptimizedEmailService(config)
    
    try:
        # 发送多个邮件测试异步处理
        for i in range(3):
            success = service.send_notification(
                task_id=f"async-test-{i:03d}",
                post_url="https://www.nodeseek.com/post-394729-1",
                comment_content=f"这是异步发送测试邮件 #{i+1}",
                comment_count=i+1,
                is_flash_sale=(i % 2 == 0),
                flash_sale_keywords=["测试", "异步"] if i % 2 == 0 else None,
                task_test_mode=True
            )
            
            print(f"异步任务 {i+1} 提交结果: {'成功' if success else '失败'}")
        
        # 等待异步任务完成
        print("等待异步任务完成...")
        time.sleep(5)
        
    finally:
        service.cleanup()


def test_connection_pool():
    """测试连接池"""
    print("\n=== 测试连接池 ===")
    
    config = OptimizedEmailConfig(
        async_enabled=True,
        max_workers=3,
        max_connections=2,
        connection_lifetime=10,  # 10秒过期
        test_mode=True
    )
    
    service = OptimizedEmailService(config)
    
    try:
        # 并发发送多个邮件，测试连接池
        def send_email(thread_id):
            for i in range(2):
                success = service.send_notification(
                    task_id=f"pool-test-{thread_id}-{i}",
                    post_url="https://www.nodeseek.com/post-394729-1",
                    comment_content=f"连接池测试邮件 - 线程{thread_id} 邮件{i+1}",
                    comment_count=i+1,
                    is_flash_sale=False,
                    task_test_mode=True
                )
                print(f"线程{thread_id} 邮件{i+1} 提交: {'成功' if success else '失败'}")
                time.sleep(1)
        
        # 创建多个线程并发发送
        threads = []
        for i in range(3):
            thread = threading.Thread(target=send_email, args=(i+1,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("等待所有邮件发送完成...")
        time.sleep(8)
        
    finally:
        service.cleanup()


def test_duplicate_prevention():
    """测试重复发送防护"""
    print("\n=== 测试重复发送防护 ===")
    
    config = OptimizedEmailConfig(
        async_enabled=False,  # 使用同步便于观察结果
        test_mode=True
    )
    
    service = OptimizedEmailService(config)
    
    try:
        # 发送相同内容的邮件
        for i in range(3):
            success = service.send_notification(
                task_id="duplicate-test",
                post_url="https://www.nodeseek.com/post-394729-1",
                comment_content="这是重复内容测试",  # 相同内容
                comment_count=1,
                is_flash_sale=False,
                task_test_mode=True
            )
            
            print(f"重复测试 {i+1}: {'成功(首次)' if success and i == 0 else '成功(重复)' if success else '失败'}")
        
    finally:
        service.cleanup()


def test_performance():
    """测试性能 - 主线程不阻塞"""
    print("\n=== 测试性能 - 主线程不阻塞 ===")
    
    config = OptimizedEmailConfig(
        async_enabled=True,
        max_workers=2,
        test_mode=True
    )
    
    service = OptimizedEmailService(config)
    
    try:
        start_time = time.time()
        
        # 快速发送多个邮件
        for i in range(5):
            success = service.send_notification(
                task_id=f"perf-test-{i:03d}",
                post_url="https://www.nodeseek.com/post-394729-1",
                comment_content=f"性能测试邮件 #{i+1}",
                comment_count=i+1,
                is_flash_sale=False,
                task_test_mode=True
            )
            print(f"邮件 {i+1} 提交: {'成功' if success else '失败'}")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"提交5个邮件任务耗时: {elapsed:.3f}秒")
        print("主线程未被阻塞，邮件在后台异步发送")
        
        # 等待后台任务完成
        print("等待后台邮件发送完成...")
        time.sleep(10)
        
    finally:
        service.cleanup()


def main():
    """主测试函数"""
    print("开始测试优化的邮件服务...")
    
    try:
        # 测试同步发送
        test_sync_sending()
        
        # 测试异步发送
        test_async_sending()
        
        # 测试连接池
        test_connection_pool()
        
        # 测试重复防护
        test_duplicate_prevention()
        
        # 测试性能
        test_performance()
        
        print("\n=== 所有测试完成 ===")
        print("请检查邮箱确认邮件接收情况")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
