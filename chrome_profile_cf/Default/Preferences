{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138, "metadata_upload_events": {"334": 1}, "secondary_form_signature_upload_events": {"265": 1}, "upload_encoding_seed": "6C041461890EF002674E5AB40F542EB1", "upload_events_last_reset_timestamp": "*****************"}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 963, "left": 245, "maximized": false, "right": 1611, "top": 195, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":1022}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "79", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "13398051129121648"}, "enterprise_profile_guid": "285e1b84-6718-4c9a-bf72-e5215f84a3ed", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "cws_info_timestamp": "13398038860946597", "install_signature": {"expire_date": "2025-10-06", "ids": ["nhkjnlcggomjhckdeamipedlomphkepc"], "invalid_ids": [], "salt": "GjtJu1k/WPDgWy6V+7GGH2XaFxADKwTkPVfQ1mp5+0s=", "signature": "irXYIyHQKp7U4uKhhOCKIxnLwsllNrPZwDCuTJCnIINJzvjTotYfvIhS3NmH6R+kKgQiNEBS2HTiBpdT4OBPRKnMIWDMOL25D7QWpijR167UOlcxDOulQ7qdRWZ1OlK2JrsK20XKyBJtJWNDXjNUeoY9Bk4hyg8le0tiY+ksOXSvcEJ9UT8hlQxne0mAa0OdJAxLsws3oybB2HLIOV3uJv+0cuZKTA2ijG2yt6M3WFxcEW8KWBVaYJLOTp3VLZ0ep0gepU8M9L2rquvUI0XQpdbJifRCrIYnp9zDZsRp4uELpzO4G5W1jKLOsYcJCFb/ic6divUkwekxkv3fFiy3Pw==", "signature_format_version": 2, "timestamp": "*****************"}, "last_chrome_version": "138.0.7204.169"}, "gaia_cookie": {"changed_time": **********.871439, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": ""}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "225750da-6085-45f2-8cb6-5e9b7b11c467"}}, "https_upgrade_navigations": {"2025-07-15": 484, "2025-07-16": 926, "2025-07-22": 310, "2025-07-23": 90, "2025-07-24": 2875, "2025-07-25": 5305, "2025-07-26": 3304, "2025-07-27": 1536}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["13398051128831912", "13398016752974624", "13397918866258738", "13397879568352964", "13397846038943781", "13397761712780766", "13397705930969735", "13397669806273367", "13397096197314342", "13397063692068824", "*****************"], "session_last_active_time": "13398066557694802", "session_start_time": "13398051128831912"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 993}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "eflJM/x7Fnf3cypGzOv5WBVuHSxP4E6ga0Ou6efkJ6kxXbav4RiloxS8vedBFZH8lEWu8JhbeRFiFwOXncES4g=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 9}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13398066567687953", "last_fetch_success": "13398066568770346"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.google.com.hk:443,*": {"last_modified": "13397714064911362", "setting": {"client_hints": [4, 5, 9, 10, 11, 13, 14, 15, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]colocrossing.com,*": {"last_modified": "13397881175893846", "setting": {}}, "https://[*.]csdn.net,*": {"last_modified": "13397714105209821", "setting": {}}, "https://[*.]example.com,*": {"last_modified": "13397067940703862", "setting": {}}, "https://[*.]google.com.hk,*": {"last_modified": "13397714064958075", "setting": {}}, "https://[*.]hostloc.com,*": {"last_modified": "13397038842308767", "setting": {}}, "https://[*.]invalid-domain-that-will-fail.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]lowendtalk.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]nodeseek.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://blog.csdn.net:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://example.com:443,*": {"expiration": "13404843959489572", "last_modified": "13397067959489576", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://hostloc.com:443,*": {"expiration": "13404814845078372", "last_modified": "13397038845078379", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://learn.microsoft.com:443,*": {"expiration": "13405490102991237", "last_modified": "13397714102991243", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://lowendtalk.com:443,*": {"expiration": "13405842524507569", "last_modified": "13398066524507579", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 26}}, "https://portal.colocrossing.com:443,*": {"expiration": "13405686098180874", "last_modified": "13397910098180882", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.google.com.hk:443,*": {"expiration": "13405490127230180", "last_modified": "13397714127230187", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.nodeseek.com:443,*": {"expiration": "13405482214234900", "last_modified": "13397706214234906", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 65}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13398066557935747", "setting": {"lastEngagementTime": 1.3398066557935728e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 59.06649622721473}}, "https://blog.csdn.net:443,*": {"last_modified": "13398016753357486", "setting": {"lastEngagementTime": 1.3397841998442072e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://example.com:443,*": {"last_modified": "13398016753357276", "setting": {"lastEngagementTime": 1.3397730155921552e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://hostloc.com:443,*": {"last_modified": "13398016753357396", "setting": {"lastEngagementTime": 1.3397700974530964e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://learn.microsoft.com:443,*": {"last_modified": "13398016753357435", "setting": {"lastEngagementTime": 1.3397841978187172e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://lowendtalk.com:443,*": {"last_modified": "13398066578910179", "setting": {"lastEngagementTime": 1.3398066578910172e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 66.92693848386274}}, "https://www.google.com.hk:443,*": {"last_modified": "13398016753357471", "setting": {"lastEngagementTime": 1.33978419389061e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "https://www.nodeseek.com:443,*": {"last_modified": "13398016753357454", "setting": {"lastEngagementTime": 1.339783401396714e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 42.365938076833174}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.97", "creation_time": "13397025663080962", "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13398066578910171", "last_time_obsolete_http_credentials_removed": 1752552123.103413, "last_time_password_store_metrics_reported": 1753543182.932538, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13397284864775765", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13398020913", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQp6nI7M2t5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEJ66kfXNruYXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAENbEu6/Bo+YXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQyMW7r8Gj5hcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAEPnEu6/Bo+YXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQ6cW7r8Gj5hcK5QIKEWNyb3NzX2RldmljZV91c2VyEs8CCsMCDQAAgD8QyuD335em5hcasAIKpwIapAIKGQ0AAIA/EhJOb0Nyb3NzRGV2aWNlVXNhZ2UKGA0AAABAEhFDcm9zc0RldmljZU1vYmlsZQoZDQAAQEASEkNyb3NzRGV2aWNlRGVza3RvcAoYDQAAgEASEUNyb3NzRGV2aWNlVGFibGV0CiINAACgQBIbQ3Jvc3NEZXZpY2VNb2JpbGVBbmREZXNrdG9wCiENAADAQBIaQ3Jvc3NEZXZpY2VNb2JpbGVBbmRUYWJsZXQKIg0AAOBAEhtDcm9zc0RldmljZURlc2t0b3BBbmRUYWJsZXQKIA0AAABBEhlDcm9zc0RldmljZUFsbERldmljZVR5cGVzChcNAAAQQRIQQ3Jvc3NEZXZpY2VPdGhlchISTm9Dcm9zc0RldmljZVVzYWdlEgQQBxgEIAIQ5uH335em5hcKZAoLc2VhcmNoX3VzZXISVQpKDQAAAAAQiMa7r8Gj5hcaOAowGi4KCg0AAIA/EgNMb3cKDQ0AAKBAEgZNZWRpdW0KCw0AALBBEgRIaWdoEgROb25lEgQQBxgEIAIQmsa7r8Gj5hc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397961599000000", "uma_in_sql_start_time": "13397025663099298"}, "sessions": {"event_log": [{"crashed": false, "time": "13398051244646374", "type": 0}, {"crashed": false, "time": "13398062868954847", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398063300032107", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398063339084681", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398063791360122", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398063865945247", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398063926184970", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398064011236143", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398064646909196", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398064662945279", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398064749770723", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398064767596685", "type": 0}, {"crashed": false, "time": "13398065112248333", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398065808435285", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398066404406763", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 2, "time": "13398066524481023", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398066555780264", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13398066556386465", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398066557683542", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"PasswordSignInPromoShownCount": 1, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 1, "translate_ignored_count_for_language": {"en": 248}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"nhkjnlcggomjhckdeamipedlomphkepc": {"cohort": "1::", "cohortname": "", "dlrc": 6781, "installdate": 6769, "pf": "********-06a2-42ba-b63a-051a6096a82b"}, "nmmhkkegccagdldgiimedpiccmgmieda": {"cohort": "1::", "cohortname": "", "dlrc": 6781, "installdate": 6770, "pf": "a8af08f8-84d9-42be-9962-e8250583c930"}}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[],[],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggesteventid\":\"-8362273092386054640\",\"google:suggesttype\":[],\"google:verbatimrelevance\":851}]"}}