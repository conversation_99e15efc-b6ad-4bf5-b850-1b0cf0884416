#!/usr/bin/env python3
"""
多论坛评论提取器
支持不同论坛的评论提取逻辑
"""

import logging
from typing import List, Dict, Tuple
from DrissionPage import ChromiumPage


class MultiForumCommentExtractor:
    """多论坛评论提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger("comment_extractor")
        
        # 不同论坛的选择器配置
        self.forum_configs = {
            "lowendtalk.com": {
                "name": "LowEndTalk",
                "selectors": [
                    "css:.Item.Comment",
                    "css:.Item",
                    "css:.Comment"
                ],
                "comment_content_selector": "css:.Message",
                "exclude_main_post": True
            },
            "nodeseek.com": {
                "name": "NodeSeek",
                "selectors": [
                    "css:article",  # 最准确的选择器
                    "css:[class*='comment']",
                    "xpath://div[contains(@class, 'comment')]"
                ],
                "comment_content_selector": None,  # 使用整个元素的文本
                "exclude_main_post": True  # 第一个 article 是主帖
            },
            "hostloc.com": {
                "name": "HostLoc",
                "selectors": [
                    "css:.plhin",
                    "css:.pls",
                    "css:td[id^='postmessage_']"
                ],
                "comment_content_selector": None,
                "exclude_main_post": True
            }
        }
    
    def detect_forum_type(self, url: str) -> str:
        """检测论坛类型"""
        for domain, config in self.forum_configs.items():
            if domain in url.lower():
                return domain
        
        # 默认返回通用配置
        return "generic"
    
    def get_comment_count_and_content(self, page: ChromiumPage, post_url: str) -> Tuple[int, List[Dict]]:
        """获取评论数量和内容"""
        forum_type = self.detect_forum_type(post_url)
        
        if forum_type == "generic":
            return self._extract_generic_comments(page)
        
        config = self.forum_configs[forum_type]
        self.logger.debug(f"使用 {config['name']} 论坛配置提取评论")
        
        return self._extract_forum_comments(page, config)

    def _get_next_page_url(self, page, current_url):
        forum_type = self.detect_forum_type(current_url)
        if forum_type == 'nodeseek.com':
            next_page_link = page.ele('css:a.next')
            return next_page_link.attr('href') if next_page_link else None
        elif forum_type == 'lowendtalk.com':
            next_page_link = page.ele('css:a.Next.Pager-nav[rel="next"]')
            return next_page_link.attr('href') if next_page_link else None
        elif forum_type == 'hostloc.com':
            next_page_link = page.ele('css:a.next')
            return next_page_link.attr('href') if next_page_link else None
        return None
    
    def _extract_forum_comments(self, page: ChromiumPage, config: Dict) -> Tuple[int, List[Dict]]:
        """使用特定论坛配置提取评论"""
        comments = []
        
        # 尝试不同的选择器
        for selector in config["selectors"]:
            try:
                elements = page.eles(selector, timeout=3)
                
                if elements:
                    self.logger.debug(f"使用选择器 {selector} 找到 {len(elements)} 个元素")
                    
                    # 处理元素
                    for i, element in enumerate(elements):
                        # 如果需要排除主帖，跳过第一个元素
                        # if config.get("exclude_main_post", False) and i == 0:
                        # if config.get("exclude_main_post", False):
                        #     continue
                        
                        try:
                            # 提取评论内容
                            if config.get("comment_content_selector"):
                                content_element = element.ele(config["comment_content_selector"], timeout=1)
                                content = content_element.text if content_element else element.text
                            else:
                                content = element.text
                            
                            if content and len(content.strip()) > 0:
                                comment_data = {
                                    "index": len(comments) + 1,
                                    "content": content.strip(),
                                    "selector_used": selector
                                }
                                comments.append(comment_data)
                                
                        except Exception as e:
                            self.logger.debug(f"提取单个评论失败: {e}")
                            continue
                    
                    # 如果找到了评论，使用这个选择器的结果
                    if comments:
                        break
                        
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue
        
        comment_count = len(comments)+1
        self.logger.info(f"提取到 {comment_count} 条评论")
        
        return comment_count, comments
    
    def _extract_generic_comments(self, page: ChromiumPage) -> Tuple[int, List[Dict]]:
        """通用评论提取逻辑"""
        self.logger.debug("使用通用评论提取逻辑")
        
        # 通用选择器列表
        generic_selectors = [
            "css:article",
            "css:.comment",
            "css:.post",
            "css:.reply",
            "css:[class*='comment']",
            "css:[class*='post']",
            "css:[class*='reply']",
            "xpath://div[contains(@class, 'comment')]",
            "xpath://div[contains(@class, 'post')]",
            "xpath://article"
        ]
        
        comments = []
        
        for selector in generic_selectors:
            try:
                elements = page.eles(selector, timeout=2)
                
                if elements and len(elements) > 1:  # 至少要有主帖+评论
                    self.logger.debug(f"通用选择器 {selector} 找到 {len(elements)} 个元素")
                    
                    # 跳过第一个元素（通常是主帖）
                    for i, element in enumerate(elements[1:], 1):
                        try:
                            content = element.text
                            if content and len(content.strip()) > 5:  # 过滤太短的内容
                                comment_data = {
                                    "index": len(comments) + 1,
                                    "content": content.strip()[:200],  # 限制长度
                                    "selector_used": selector
                                }
                                comments.append(comment_data)
                        except:
                            continue
                    
                    # 如果找到了合理数量的评论，使用这个结果
                    if len(comments) > 0:
                        break
                        
            except Exception as e:
                self.logger.debug(f"通用选择器 {selector} 失败: {e}")
                continue
        
        comment_count = len(comments)
        self.logger.info(f"通用提取到 {comment_count} 条评论")
        
        return comment_count, comments
    
    def test_extraction(self, page: ChromiumPage, post_url: str) -> Dict:
        """测试评论提取功能"""
        self.logger.info(f"测试评论提取: {post_url}")
        
        forum_type = self.detect_forum_type(post_url)
        
        try:
            comment_count, comments = self.get_comment_count_and_content(page, post_url)
            
            result = {
                "success": True,
                "forum_type": forum_type,
                "comment_count": comment_count,
                "comments_preview": comments[:3],  # 只显示前3条
                "page_title": page.title,
                "page_url": page.url
            }
            
            self.logger.info(f"✅ 提取成功: {comment_count} 条评论")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 提取失败: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "forum_type": forum_type,
                "page_title": page.title if hasattr(page, 'title') else "Unknown",
                "page_url": page.url if hasattr(page, 'url') else "Unknown"
            }


def test_comment_extraction():
    """测试评论提取功能"""
    from DrissionPage import ChromiumPage, ChromiumOptions
    
    # 创建浏览器页面
    options = ChromiumOptions()
    options.headless(False)
    options.set_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36")
    options.set_argument("--no-sandbox")
    options.set_argument("--disable-blink-features=AutomationControlled")
    
    page = ChromiumPage(addr_or_opts=options)
    extractor = MultiForumCommentExtractor()
    
    # 测试 URL
    test_urls = [
        "https://www.nodeseek.com/post-393807-1#3",
        # "https://lowendtalk.com/discussion/12345/test-post",  # 如果有的话
    ]
    
    for url in test_urls:
        print(f"\n🧪 测试 URL: {url}")
        print("=" * 60)
        
        try:
            # 导航到页面
            page.get(url)
            time.sleep(5)
            
            # 测试提取
            result = extractor.test_extraction(page, url)
            
            # 显示结果
            print(f"论坛类型: {result['forum_type']}")
            print(f"页面标题: {result['page_title']}")
            print(f"提取状态: {'成功' if result['success'] else '失败'}")
            
            if result['success']:
                print(f"评论数量: {result['comment_count']}")
                print("评论预览:")
                for i, comment in enumerate(result['comments_preview'], 1):
                    print(f"  {i}. {comment['content'][:100]}...")
                    print(f"     (使用选择器: {comment.get('selector_used', 'unknown')})")
            else:
                print(f"错误: {result['error']}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    page.close()


if __name__ == "__main__":
    import time
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    test_comment_extraction()
