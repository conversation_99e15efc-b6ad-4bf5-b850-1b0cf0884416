#!/usr/bin/env python3
"""
真正工作的邮件通知服务
使用QQ SMTP发送真实邮件
支持异步发送和连接池管理
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
import threading
import time
import hashlib
from datetime import datetime, timezone
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
import queue
import concurrent.futures
from contextlib import contextmanager


@dataclass
class RealEmailConfig:
    """真实邮件配置"""
    smtp_server: str = "smtp.qq.com"
    smtp_port: int = 465
    username: str = "<EMAIL>"
    password: str = "majvjpnlzjahfgei"
    recipient: str = "<EMAIL>"
    enabled: bool = True
    test_mode: bool = False
    timeout: int = 30


class EmailNotificationService:
    """真正工作的邮件通知服务"""
    
    def __init__(self, config: RealEmailConfig = None):
        self.config = config or RealEmailConfig()
        self.lock = threading.Lock()
        self.sent_notifications: Dict[str, Set[str]] = {}
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger("real_email_service")
        
        # 移除现有处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 创建文件处理器
        file_handler = logging.FileHandler('real_email_service.log', encoding='utf-8')
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(file_handler)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(console_handler)
        
        self.logger.setLevel(logging.INFO)
        self.logger.info("真实邮件通知服务已初始化")
        
    def send_comment_notification(self, task_id: str, post_url: str, comment_content: str,
                                comment_count: int, is_flash_sale: bool = False,
                                flash_sale_keywords: List[str] = None,
                                task_test_mode: bool = None) -> bool:
        """发送真实的邮件通知"""
        
        if not self.config.enabled:
            self.logger.debug("邮件通知已禁用")
            return False
            
        # 创建评论哈希用于重复检测
        comment_hash = hashlib.md5(
            f"{task_id}:{post_url}:{comment_content}".encode('utf-8')
        ).hexdigest()
        
        # 检查重复
        with self.lock:
            if task_id not in self.sent_notifications:
                self.sent_notifications[task_id] = set()
                
            if comment_hash in self.sent_notifications[task_id]:
                self.logger.debug(f"防止重复通知 task {task_id}")
                return True
                
            self.sent_notifications[task_id].add(comment_hash)
        
        try:
            # 创建邮件内容
            effective_test_mode = task_test_mode if task_test_mode is not None else self.config.test_mode
            
            subject = self._create_subject(is_flash_sale, effective_test_mode)
            body = self._create_email_body(
                post_url, comment_content, comment_count,
                is_flash_sale, flash_sale_keywords or [], effective_test_mode
            )
            
            # 发送真实邮件
            success = self._send_real_email(subject, body)
            
            if success:
                self.logger.info(f"真实邮件通知已发送 task {task_id} (flash_sale={is_flash_sale})")
                print(f"📧 真实邮件已发送: {subject}")
            else:
                # 如果邮件发送失败，从已发送集合中移除
                with self.lock:
                    self.sent_notifications[task_id].discard(comment_hash)
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送邮件通知错误: {e}")
            # 如果发生错误，从已发送集合中移除
            with self.lock:
                if task_id in self.sent_notifications:
                    self.sent_notifications[task_id].discard(comment_hash)
            return False
    
    def _create_subject(self, is_flash_sale: bool, test_mode: bool) -> str:
        """创建邮件主题"""
        if is_flash_sale:
            subject = "🚨 NodeSeek闪购提醒: 检测到新的闪购信息"
        else:
            subject = "📢 NodeSeek论坛监控: 检测到新评论"
            
        if test_mode:
            subject += " (测试模式)"
            
        return subject
    
    def _create_email_body(self, post_url: str, comment_content: str, comment_count: int,
                          is_flash_sale: bool, flash_sale_keywords: List[str], test_mode: bool) -> str:
        """创建邮件正文"""
        
        # 截断和清理评论内容
        safe_comment = comment_content[:200] if comment_content else "无内容"
        
        body = f"""
NodeSeek论坛监控系统检测到新的{'闪购信息' if is_flash_sale else '评论'}:

帖子链接: {post_url}
评论内容: {safe_comment}
当前评论总数: {comment_count}
闪购检测: {'是' if is_flash_sale else '否'}
"""
        
        if is_flash_sale and flash_sale_keywords:
            body += f"闪购关键词: {', '.join(flash_sale_keywords)}\n"
        
        if test_mode:
            body += "\n[测试模式] 这是一个测试通知。\n"
        
        body += f"""
检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

--
NodeSeek论坛监控系统
自动化通知服务
"""
        
        return body
    
    def _send_real_email(self, subject: str, body: str, max_retries: int = 3) -> bool:
        """发送真实邮件 - 使用多种方法尝试"""

        # 方法1: 尝试SMTP_SSL
        for attempt in range(max_retries):
            try:
                msg = MIMEMultipart()
                msg['From'] = self.config.username
                msg['To'] = self.config.recipient
                msg['Subject'] = subject
                msg.attach(MIMEText(body, 'plain'))
                # 修改邮件发送逻辑，不使用 with 语句
                server = smtplib.SMTP_SSL(self.config.smtp_server, self.config.smtp_port)
                server.login(self.config.username, self.config.password)
                server.sendmail(self.config.username, self.config.recipient, msg.as_string())
                server.quit()
                self.logger.info(f"邮件发送成功 (SMTP, 尝试 {attempt + 1})")
                return True
            except smtplib.SMTPAuthenticationError as e:
                self.logger.error(f"SMTP认证失败: {e}")
                break  # 不重试认证失败

            except Exception as e:
                self.logger.error(f"SSL方法失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)

        self.logger.error(f"所有邮件发送方法都失败了")
        return False


# 全局实例
real_email_service = EmailNotificationService()


def send_test_real_email() -> bool:
    """发送测试真实邮件"""
    return real_email_service.send_comment_notification(
        task_id="real-test",
        post_url="https://www.nodeseek.com/post-394729-1",
        comment_content="这是真实邮件服务的测试通知。",
        comment_count=1,
        is_flash_sale=False,
        task_test_mode=True
    )


if __name__ == "__main__":
    print("测试真实邮件通知服务...")
    success = send_test_real_email()
    
    if success:
        print("✅ 真实邮件服务测试成功!")
        print("📧 请检查您的QQ邮箱")
    else:
        print("❌ 真实邮件服务测试失败")
