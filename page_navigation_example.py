#!/usr/bin/env python3
"""
页面导航功能使用示例
展示如何从指定页面开始监控，以及翻页逻辑
"""

import requests
import json
from page_navigation_manager import PageNavigationManager


def create_task_from_specific_page():
    """创建从指定页面开始的监控任务"""
    print("=== 创建从指定页面开始的监控任务 ===\n")
    
    # 示例1: 从LowEndTalk第20页开始监控
    print("示例1: 从LowEndTalk第20页开始监控")
    lowendtalk_url = "https://lowendtalk.com/discussion/207804/the-ultimate-fat32-transfer-megathread/p20"
    
    nav_manager = PageNavigationManager()
    base_url, start_page = nav_manager.parse_page_from_url(lowendtalk_url)
    forum_type = nav_manager.detect_forum_type(lowendtalk_url)
    expected_comments = nav_manager.get_expected_comment_count_for_page(start_page, forum_type)
    
    print(f"URL: {lowendtalk_url}")
    print(f"论坛类型: {forum_type}")
    print(f"起始页码: {start_page}")
    print(f"预期之前评论数: {expected_comments}")
    print(f"每页评论数: {nav_manager.get_forum_config(forum_type)['comments_per_page']}")
    
    # 创建任务的API调用示例
    task_data = {
        "post_url": lowendtalk_url,  # 直接使用包含页码的URL
        "forum_domain": "lowendtalk.com",
        "monitor_interval": 60,
        "ai_analysis_enabled": True,
        "email_notifications_enabled": True,
        "email_test_mode": False
    }
    
    print(f"\n创建任务的API调用:")
    print(f"POST /tasks")
    print(f"Body: {json.dumps(task_data, indent=2)}")
    print(f"说明: 系统会自动识别从第{start_page}页开始监控，并设置初始评论数为{expected_comments}")
    print()
    
    # 示例2: 从NodeSeek第5页开始监控
    print("示例2: 从NodeSeek第5页开始监控")
    nodeseek_url = "https://www.nodeseek.com/post-394729-1?page=5"
    
    base_url, start_page = nav_manager.parse_page_from_url(nodeseek_url)
    forum_type = nav_manager.detect_forum_type(nodeseek_url)
    expected_comments = nav_manager.get_expected_comment_count_for_page(start_page, forum_type)
    
    print(f"URL: {nodeseek_url}")
    print(f"论坛类型: {forum_type}")
    print(f"起始页码: {start_page}")
    print(f"预期之前评论数: {expected_comments}")
    print(f"每页评论数: {nav_manager.get_forum_config(forum_type)['comments_per_page']}")
    
    task_data = {
        "post_url": nodeseek_url,
        "forum_domain": "nodeseek.com",
        "monitor_interval": 45,
        "ai_analysis_enabled": True,
        "email_notifications_enabled": True,
        "email_test_mode": True
    }
    
    print(f"\n创建任务的API调用:")
    print(f"POST /tasks")
    print(f"Body: {json.dumps(task_data, indent=2)}")
    print(f"说明: 系统会自动识别从第{start_page}页开始监控，并设置初始评论数为{expected_comments}")
    print()


def demonstrate_pagination_logic():
    """演示翻页逻辑"""
    print("=== 翻页逻辑演示 ===\n")
    
    nav_manager = PageNavigationManager()
    
    # LowEndTalk翻页示例
    print("LowEndTalk翻页示例:")
    base_url = "https://lowendtalk.com/discussion/207804/title"
    current_page = 20
    
    print(f"当前页面: 第{current_page}页")
    current_url = nav_manager.build_page_url(base_url, current_page, "lowendtalk.com")
    print(f"当前URL: {current_url}")
    
    # 模拟检测到需要翻页的情况
    print("\n翻页条件检查:")
    print("1. 当前页面评论数量达到30条 (单页最大值)")
    print("2. 检测到可点击的'下一页'按钮")
    print("满足条件 -> 翻到下一页")
    
    next_page = current_page + 1
    next_url = nav_manager.build_page_url(base_url, next_page, "lowendtalk.com")
    print(f"\n翻页后:")
    print(f"新页码: 第{next_page}页")
    print(f"新URL: {next_url}")
    print("任务URL已更新到数据库")
    print()
    
    # NodeSeek翻页示例
    print("NodeSeek翻页示例:")
    base_url = "https://www.nodeseek.com/post-394729-1"
    current_page = 5
    
    print(f"当前页面: 第{current_page}页")
    current_url = nav_manager.build_page_url(base_url, current_page, "nodeseek.com")
    print(f"当前URL: {current_url}")
    
    print("\n翻页条件检查:")
    print("1. 当前页面评论数量达到10条 (单页最大值)")
    print("2. 检测到可点击的'下一页'按钮")
    print("满足条件 -> 翻到下一页")
    
    next_page = current_page + 1
    next_url = nav_manager.build_page_url(base_url, next_page, "nodeseek.com")
    print(f"\n翻页后:")
    print(f"新页码: 第{next_page}页")
    print(f"新URL: {next_url}")
    print("任务URL已更新到数据库")
    print()


def show_monitoring_workflow():
    """展示监控工作流程"""
    print("=== 监控工作流程 ===\n")
    
    print("1. 任务初始化:")
    print("   - 解析起始URL，识别论坛类型和页码")
    print("   - 计算预期的初始评论数量")
    print("   - 设置last_comment_count为预期值")
    print()
    
    print("2. 监控循环:")
    print("   - 刷新当前页面")
    print("   - 获取当前评论总数")
    print("   - 如果有新评论，处理增量评论")
    print("   - 检查是否需要翻页")
    print("   - 更新任务统计信息")
    print()
    
    print("3. 翻页检查:")
    print("   - 当前页评论数 >= 单页最大值")
    print("   - 或者检测到可用的'下一页'按钮")
    print("   - 满足条件则导航到下一页")
    print("   - 更新任务URL到数据库")
    print()
    
    print("4. 增量处理:")
    print("   - 只处理新增的评论")
    print("   - 避免重复处理已监控的评论")
    print("   - 支持跨页面的增量监控")
    print()


def show_api_usage():
    """展示API使用方法"""
    print("=== API使用方法 ===\n")
    
    print("创建从指定页面开始的监控任务:")
    print("POST http://localhost:8000/tasks")
    print("Content-Type: application/json")
    print()
    
    # LowEndTalk示例
    print("LowEndTalk示例 (从第20页开始):")
    lowendtalk_task = {
        "post_url": "https://lowendtalk.com/discussion/207804/title/p20",
        "forum_domain": "lowendtalk.com",
        "monitor_interval": 60,
        "ai_analysis_enabled": True,
        "email_notifications_enabled": True,
        "email_test_mode": False
    }
    print(json.dumps(lowendtalk_task, indent=2))
    print()
    
    # NodeSeek示例
    print("NodeSeek示例 (从第5页开始):")
    nodeseek_task = {
        "post_url": "https://www.nodeseek.com/post-394729-1?page=5",
        "forum_domain": "nodeseek.com", 
        "monitor_interval": 45,
        "ai_analysis_enabled": True,
        "email_notifications_enabled": True,
        "email_test_mode": True
    }
    print(json.dumps(nodeseek_task, indent=2))
    print()
    
    print("系统会自动:")
    print("✅ 识别论坛类型和页码")
    print("✅ 计算预期的初始评论数")
    print("✅ 设置正确的监控起点")
    print("✅ 处理后续的翻页逻辑")
    print("✅ 更新任务URL到数据库")
    print()


def show_supported_forums():
    """显示支持的论坛格式"""
    print("=== 支持的论坛格式 ===\n")
    
    nav_manager = PageNavigationManager()
    
    for forum_type, config in nav_manager.forum_configs.items():
        print(f"论坛: {config['name']} ({forum_type})")
        print(f"  每页评论数: {config['comments_per_page']}")
        print(f"  URL格式: {config['page_url_pattern']}")
        print(f"  第一页后缀: '{config['first_page_suffix']}'")
        
        # 示例URL
        if forum_type == "lowendtalk.com":
            base = "https://lowendtalk.com/discussion/12345/title"
            print(f"  示例:")
            print(f"    第1页: {nav_manager.build_page_url(base, 1, forum_type)}")
            print(f"    第2页: {nav_manager.build_page_url(base, 2, forum_type)}")
            print(f"    第20页: {nav_manager.build_page_url(base, 20, forum_type)}")
            
        elif forum_type == "nodeseek.com":
            base = "https://www.nodeseek.com/post-12345-1"
            print(f"  示例:")
            print(f"    第1页: {nav_manager.build_page_url(base, 1, forum_type)}")
            print(f"    第2页: {nav_manager.build_page_url(base, 2, forum_type)}")
            print(f"    第5页: {nav_manager.build_page_url(base, 5, forum_type)}")
            
        elif forum_type == "hostloc.com":
            base = "https://www.hostloc.com/thread-12345"
            print(f"  示例:")
            print(f"    第1页: {nav_manager.build_page_url(base, 1, forum_type)}")
            print(f"    第2页: {nav_manager.build_page_url(base, 2, forum_type)}")
            print(f"    第10页: {nav_manager.build_page_url(base, 10, forum_type)}")
        
        print()


def main():
    """主函数"""
    print("页面导航功能使用指南\n")
    
    show_supported_forums()
    create_task_from_specific_page()
    demonstrate_pagination_logic()
    show_monitoring_workflow()
    show_api_usage()
    
    print("=== 总结 ===")
    print("✅ 支持从任意页面开始监控")
    print("✅ 自动计算初始评论数量")
    print("✅ 智能翻页检测和导航")
    print("✅ 跨页面增量评论处理")
    print("✅ 自动更新任务URL")
    print("✅ 支持多种论坛格式")
    print("\n现在你可以:")
    print("1. 直接使用包含页码的URL创建任务")
    print("2. 系统会自动从指定页面开始监控")
    print("3. 当页面满时自动翻到下一页")
    print("4. 任务URL会实时更新到数据库")


if __name__ == "__main__":
    main()
