#!/usr/bin/env python3
"""
测试验证错误修复
验证 Pydantic 验证和评论处理错误是否已修复
"""

import sys
import os
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

try:
    from task_management_api import TaskManager, TaskCreate
    from task_execution_engine import TaskExecutor
    print("✅ 成功导入相关模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_pydantic_validation():
    """测试 Pydantic 验证是否正常"""
    print("=== 测试 Pydantic 验证 ===\n")
    
    db_path = "crawling_tasks.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        task_manager = TaskManager(db_path)
        
        # 获取所有任务，这会触发 Pydantic 验证
        tasks = task_manager.get_all_tasks()
        print(f"✅ 成功获取 {len(tasks)} 个任务，Pydantic 验证通过")
        
        # 检查每个任务的字段类型
        for i, task in enumerate(tasks[:3]):  # 只检查前3个
            print(f"\n任务 {i+1}: {task.id[:8]}...")
            print(f"  email_notifications_enabled: {task.email_notifications_enabled} ({type(task.email_notifications_enabled).__name__})")
            print(f"  email_test_mode: {task.email_test_mode} ({type(task.email_test_mode).__name__})")
            print(f"  status: {task.status} ({type(task.status).__name__})")
            print(f"  comment_count: {task.comment_count} ({type(task.comment_count).__name__})")
        
        return True
        
    except Exception as e:
        print(f"❌ Pydantic 验证失败: {e}")
        return False


def test_comment_content_extraction():
    """测试评论内容提取"""
    print("\n=== 测试评论内容提取 ===\n")
    
    # 创建临时TaskExecutor实例来测试评论提取
    try:
        from crawler_config import CrawlerConfig
        from task_browser_manager import TaskBrowserManager
        
        config = CrawlerConfig()
        browser_manager = TaskBrowserManager(config)
        task_manager = TaskManager("crawling_tasks.db")
        
        # 创建TaskExecutor实例
        executor = TaskExecutor("test-task", config, browser_manager, task_manager)
        
        # 测试不同类型的评论数据
        test_cases = [
            {
                "name": "字典格式评论",
                "comment_data": {
                    "index": 1,
                    "content": "这是一个测试评论内容",
                    "selector_used": "css:article"
                },
                "expected": "这是一个测试评论内容"
            },
            {
                "name": "空字典",
                "comment_data": {},
                "expected": ""
            },
            {
                "name": "只有content字段的字典",
                "comment_data": {"content": "简单评论"},
                "expected": "简单评论"
            },
            {
                "name": "None值",
                "comment_data": None,
                "expected": ""
            }
        ]
        
        for case in test_cases:
            try:
                result = executor._extract_comment_content(case["comment_data"])
                expected = case["expected"]
                
                if result == expected:
                    print(f"✅ {case['name']}: '{result}'")
                else:
                    print(f"❌ {case['name']}: 期望 '{expected}', 实际 '{result}'")
                    
            except Exception as e:
                print(f"❌ {case['name']}: 提取失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 评论提取测试失败: {e}")
        return False


def test_task_creation_and_validation():
    """测试任务创建和验证"""
    print("\n=== 测试任务创建和验证 ===\n")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        task_manager = TaskManager(db_path)
        
        # 创建测试任务
        task_data = TaskCreate(
            post_url="https://test.com/post/123",
            forum_domain="test.com",
            monitor_interval=60,
            ai_analysis_enabled=True,
            email_notifications_enabled=True,
            email_test_mode=False
        )
        
        task = task_manager.create_task(task_data)
        print(f"✅ 创建任务成功: {task.id}")
        
        # 验证任务字段类型
        print(f"  email_notifications_enabled: {task.email_notifications_enabled} ({type(task.email_notifications_enabled).__name__})")
        print(f"  email_test_mode: {task.email_test_mode} ({type(task.email_test_mode).__name__})")
        print(f"  status: {task.status} ({type(task.status).__name__})")
        print(f"  comment_count: {task.comment_count} ({type(task.comment_count).__name__})")
        
        # 测试获取任务
        retrieved_task = task_manager.get_task(task.id)
        if retrieved_task:
            print(f"✅ 获取任务成功，Pydantic 验证通过")
        else:
            print(f"❌ 获取任务失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务创建和验证失败: {e}")
        return False
    
    finally:
        try:
            os.unlink(db_path)
        except Exception:
            pass


def test_real_database_validation():
    """测试真实数据库的验证"""
    print("\n=== 测试真实数据库验证 ===\n")
    
    db_path = "crawling_tasks.db"
    if not os.path.exists(db_path):
        print(f"❌ 真实数据库文件不存在: {db_path}")
        return False
    
    try:
        task_manager = TaskManager(db_path)
        
        # 获取所有任务
        tasks = task_manager.get_all_tasks()
        print(f"✅ 成功获取 {len(tasks)} 个任务")
        
        # 验证每个任务
        valid_count = 0
        for task in tasks:
            try:
                # 尝试访问所有字段，触发验证
                _ = task.id
                _ = task.post_url
                _ = task.email_notifications_enabled
                _ = task.email_test_mode
                _ = task.status
                _ = task.comment_count
                valid_count += 1
            except Exception as e:
                print(f"❌ 任务 {task.id} 验证失败: {e}")
        
        print(f"✅ {valid_count}/{len(tasks)} 个任务验证通过")
        
        if valid_count == len(tasks):
            print("✅ 所有任务验证成功")
            return True
        else:
            print("❌ 部分任务验证失败")
            return False
        
    except Exception as e:
        print(f"❌ 真实数据库验证失败: {e}")
        return False


def main():
    """主测试函数"""
    print("验证错误修复测试\n")
    
    results = []
    
    # 测试1: Pydantic 验证
    results.append(("Pydantic验证", test_pydantic_validation()))
    
    # 测试2: 评论内容提取
    results.append(("评论内容提取", test_comment_content_extraction()))
    
    # 测试3: 任务创建和验证
    results.append(("任务创建和验证", test_task_creation_and_validation()))
    
    # 测试4: 真实数据库验证
    results.append(("真实数据库验证", test_real_database_validation()))
    
    # 总结结果
    print("\n=== 测试结果总结 ===")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有验证错误已修复！")
        print("✅ Pydantic 验证正常工作")
        print("✅ 评论处理不再出错")
        print("✅ 数据库数据格式正确")
        print("✅ 任务执行引擎可以正常运行")
    else:
        print("\n⚠️  仍有部分问题需要解决")


if __name__ == "__main__":
    main()
