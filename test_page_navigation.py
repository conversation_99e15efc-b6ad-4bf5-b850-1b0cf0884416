#!/usr/bin/env python3
"""
测试页面导航功能
验证不同论坛的URL解析、页面构建、翻页逻辑
"""

from page_navigation_manager import PageNavigationManager


def test_lowendtalk_navigation():
    """测试LowEndTalk论坛的页面导航"""
    print("=== 测试 LowEndTalk 论坛导航 ===")
    
    nav_manager = PageNavigationManager()
    
    # 测试URL解析
    test_urls = [
        "https://lowendtalk.com/discussion/207804/the-ultimate-fat32-transfer-megathread",
        "https://lowendtalk.com/discussion/207804/the-ultimate-fat32-transfer-megathread/p20",
        "https://lowendtalk.com/discussion/207804/the-ultimate-fat32-transfer-megathread/p5"
    ]
    
    for url in test_urls:
        base_url, page_num = nav_manager.parse_page_from_url(url)
        print(f"URL: {url}")
        print(f"  基础URL: {base_url}")
        print(f"  页码: {page_num}")
        
        # 测试重新构建URL
        rebuilt_url = nav_manager.build_page_url(base_url, page_num, "lowendtalk.com")
        print(f"  重建URL: {rebuilt_url}")
        print(f"  匹配: {'✅' if rebuilt_url == url else '❌'}")
        print()
    
    # 测试评论数量计算
    print("评论数量与页码计算:")
    test_comments = [1, 30, 31, 60, 61, 570, 600]
    for comment_count in test_comments:
        page_num = nav_manager.calculate_page_from_comment_count(comment_count, "lowendtalk.com")
        expected_comments = nav_manager.get_expected_comment_count_for_page(page_num, "lowendtalk.com")
        print(f"  {comment_count} 条评论 -> 第 {page_num} 页 (预期前面有 {expected_comments} 条)")
    
    print()


def test_nodeseek_navigation():
    """测试NodeSeek论坛的页面导航"""
    print("=== 测试 NodeSeek 论坛导航 ===")
    
    nav_manager = PageNavigationManager()
    
    # 测试URL解析
    test_urls = [
        "https://www.nodeseek.com/post-394729-1",
        "https://www.nodeseek.com/post-394729-1?page=2",
        "https://www.nodeseek.com/post-394729-1?page=5&other=param"
    ]
    
    for url in test_urls:
        base_url, page_num = nav_manager.parse_page_from_url(url)
        print(f"URL: {url}")
        print(f"  基础URL: {base_url}")
        print(f"  页码: {page_num}")
        
        # 测试重新构建URL
        rebuilt_url = nav_manager.build_page_url(base_url, page_num, "nodeseek.com")
        print(f"  重建URL: {rebuilt_url}")
        print()
    
    # 测试评论数量计算
    print("评论数量与页码计算:")
    test_comments = [1, 10, 11, 20, 21, 95, 100]
    for comment_count in test_comments:
        page_num = nav_manager.calculate_page_from_comment_count(comment_count, "nodeseek.com")
        expected_comments = nav_manager.get_expected_comment_count_for_page(page_num, "nodeseek.com")
        print(f"  {comment_count} 条评论 -> 第 {page_num} 页 (预期前面有 {expected_comments} 条)")
    
    print()


def test_hostloc_navigation():
    """测试HostLoc论坛的页面导航"""
    print("=== 测试 HostLoc 论坛导航 ===")
    
    nav_manager = PageNavigationManager()
    
    # 测试URL解析
    test_urls = [
        "https://www.hostloc.com/thread-123456-1-1.html",
        "https://www.hostloc.com/thread-123456-2-1.html",
        "https://www.hostloc.com/thread-123456-10-1.html"
    ]
    
    for url in test_urls:
        base_url, page_num = nav_manager.parse_page_from_url(url)
        print(f"URL: {url}")
        print(f"  基础URL: {base_url}")
        print(f"  页码: {page_num}")
        
        # 测试重新构建URL
        rebuilt_url = nav_manager.build_page_url(base_url, page_num, "hostloc.com")
        print(f"  重建URL: {rebuilt_url}")
        print(f"  匹配: {'✅' if rebuilt_url == url else '❌'}")
        print()
    
    # 测试评论数量计算
    print("评论数量与页码计算:")
    test_comments = [1, 20, 21, 40, 41, 190, 200]
    for comment_count in test_comments:
        page_num = nav_manager.calculate_page_from_comment_count(comment_count, "hostloc.com")
        expected_comments = nav_manager.get_expected_comment_count_for_page(page_num, "hostloc.com")
        print(f"  {comment_count} 条评论 -> 第 {page_num} 页 (预期前面有 {expected_comments} 条)")
    
    print()


def test_forum_detection():
    """测试论坛类型检测"""
    print("=== 测试论坛类型检测 ===")
    
    nav_manager = PageNavigationManager()
    
    test_urls = [
        "https://lowendtalk.com/discussion/207804/title",
        "https://www.nodeseek.com/post-394729-1",
        "https://www.hostloc.com/thread-123456-1-1.html",
        "https://unknown-forum.com/post/123"
    ]
    
    for url in test_urls:
        forum_type = nav_manager.detect_forum_type(url)
        config = nav_manager.get_forum_config(forum_type)
        print(f"URL: {url}")
        print(f"  论坛类型: {forum_type}")
        print(f"  每页评论数: {config.get('comments_per_page', 'N/A')}")
        print(f"  页面URL模式: {config.get('page_url_pattern', 'N/A')}")
        print()


def test_page_building():
    """测试页面URL构建"""
    print("=== 测试页面URL构建 ===")
    
    nav_manager = PageNavigationManager()
    
    # LowEndTalk
    base_url = "https://lowendtalk.com/discussion/207804/title"
    print(f"LowEndTalk 基础URL: {base_url}")
    for page in [1, 2, 5, 20]:
        url = nav_manager.build_page_url(base_url, page, "lowendtalk.com")
        print(f"  第 {page} 页: {url}")
    print()
    
    # NodeSeek
    base_url = "https://www.nodeseek.com/post-394729-1"
    print(f"NodeSeek 基础URL: {base_url}")
    for page in [1, 2, 5, 10]:
        url = nav_manager.build_page_url(base_url, page, "nodeseek.com")
        print(f"  第 {page} 页: {url}")
    print()
    
    # HostLoc
    base_url = "https://www.hostloc.com/thread-123456"
    print(f"HostLoc 基础URL: {base_url}")
    for page in [1, 2, 5, 10]:
        url = nav_manager.build_page_url(base_url, page, "hostloc.com")
        print(f"  第 {page} 页: {url}")
    print()


def test_monitoring_scenario():
    """测试监控场景"""
    print("=== 测试监控场景 ===")
    
    nav_manager = PageNavigationManager()
    
    # 场景1: 从LowEndTalk第20页开始监控
    print("场景1: 从LowEndTalk第20页开始监控")
    start_url = "https://lowendtalk.com/discussion/207804/title/p20"
    base_url, start_page = nav_manager.parse_page_from_url(start_url)
    expected_comments = nav_manager.get_expected_comment_count_for_page(start_page, "lowendtalk.com")
    
    print(f"  起始URL: {start_url}")
    print(f"  起始页码: {start_page}")
    print(f"  预期之前评论数: {expected_comments}")
    print(f"  论坛类型: {nav_manager.detect_forum_type(start_url)}")
    print()
    
    # 场景2: 从NodeSeek第5页开始监控
    print("场景2: 从NodeSeek第5页开始监控")
    start_url = "https://www.nodeseek.com/post-394729-1?page=5"
    base_url, start_page = nav_manager.parse_page_from_url(start_url)
    expected_comments = nav_manager.get_expected_comment_count_for_page(start_page, "nodeseek.com")
    
    print(f"  起始URL: {start_url}")
    print(f"  起始页码: {start_page}")
    print(f"  预期之前评论数: {expected_comments}")
    print(f"  论坛类型: {nav_manager.detect_forum_type(start_url)}")
    print()


def main():
    """主测试函数"""
    print("页面导航管理器测试\n")
    
    test_forum_detection()
    test_lowendtalk_navigation()
    test_nodeseek_navigation()
    test_hostloc_navigation()
    test_page_building()
    test_monitoring_scenario()
    
    print("=== 测试完成 ===")
    print("页面导航功能已验证，支持:")
    print("✅ LowEndTalk: /p{page} 格式，30评论/页")
    print("✅ NodeSeek: ?page={page} 格式，10评论/页")
    print("✅ HostLoc: -{page}-1.html 格式，20评论/页")
    print("✅ URL解析和重建")
    print("✅ 评论数量与页码计算")
    print("✅ 从指定页面开始监控")


if __name__ == "__main__":
    main()
