#!/usr/bin/env python3
"""
数据库迁移脚本：删除 post_url 字段的唯一约束
解决翻页时的 UNIQUE constraint failed 错误
"""

import sqlite3
import os
import shutil
from datetime import datetime


def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None


def check_current_schema(db_path):
    """检查当前数据库结构"""
    print("=== 检查当前数据库结构 ===")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tasks'")
            result = cursor.fetchone()
            
            if result:
                create_sql = result[0]
                print(f"当前表结构:\n{create_sql}")
                
                # 检查是否有UNIQUE约束
                if "post_url TEXT UNIQUE" in create_sql:
                    print("❌ 发现 post_url 字段有 UNIQUE 约束")
                    return True
                else:
                    print("✅ post_url 字段没有 UNIQUE 约束")
                    return False
            else:
                print("❌ 未找到 tasks 表")
                return False
                
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        return False


def get_existing_data(db_path):
    """获取现有数据"""
    print("=== 获取现有数据 ===")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有任务数据
            cursor.execute("SELECT * FROM tasks")
            tasks = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(tasks)")
            columns = [row[1] for row in cursor.fetchall()]
            
            print(f"✅ 找到 {len(tasks)} 个任务")
            print(f"✅ 表列: {columns}")
            
            return tasks, columns
            
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None, None


def remove_unique_constraint(db_path):
    """删除 post_url 的唯一约束"""
    print("=== 删除 UNIQUE 约束 ===")
    
    # 1. 备份数据库
    backup_path = backup_database(db_path)
    if not backup_path:
        return False
    
    # 2. 检查当前结构
    has_unique = check_current_schema(db_path)
    if not has_unique:
        print("✅ 无需删除约束，已经没有 UNIQUE 约束")
        return True
    
    # 3. 获取现有数据
    tasks_data, columns = get_existing_data(db_path)
    if tasks_data is None:
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 4. 删除旧表
            print("🗑️  删除旧表...")
            cursor.execute("DROP TABLE IF EXISTS tasks_old")
            cursor.execute("ALTER TABLE tasks RENAME TO tasks_old")
            
            # 5. 创建新表（没有UNIQUE约束）
            print("🔧 创建新表结构...")
            cursor.execute("""
                CREATE TABLE tasks (
                    id TEXT PRIMARY KEY,
                    post_url TEXT NOT NULL,
                    forum_domain TEXT NOT NULL,
                    monitor_interval INTEGER NOT NULL,
                    ai_analysis_enabled BOOLEAN NOT NULL,
                    email_notifications_enabled BOOLEAN DEFAULT 1,
                    email_test_mode BOOLEAN DEFAULT 1,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    stopped_at TEXT,
                    last_check_at TEXT,
                    comment_count INTEGER DEFAULT 0,
                    flash_sales_found INTEGER DEFAULT 0,
                    error_message TEXT
                )
            """)
            
            # 6. 迁移数据
            print("📦 迁移数据...")
            placeholders = ','.join(['?' for _ in columns])
            cursor.executemany(f"INSERT INTO tasks VALUES ({placeholders})", tasks_data)
            
            # 7. 删除旧表
            cursor.execute("DROP TABLE tasks_old")
            
            conn.commit()
            print("✅ 约束删除成功！")
            
            # 8. 验证新结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tasks'")
            new_schema = cursor.fetchone()[0]
            print(f"新表结构:\n{new_schema}")
            
            return True
            
    except Exception as e:
        print(f"❌ 删除约束失败: {e}")
        
        # 尝试恢复备份
        try:
            shutil.copy2(backup_path, db_path)
            print(f"✅ 已从备份恢复: {backup_path}")
        except Exception as restore_error:
            print(f"❌ 恢复备份失败: {restore_error}")
        
        return False


def verify_migration(db_path):
    """验证迁移结果"""
    print("=== 验证迁移结果 ===")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tasks'")
            schema = cursor.fetchone()[0]
            
            if "post_url TEXT UNIQUE" in schema:
                print("❌ UNIQUE 约束仍然存在")
                return False
            elif "post_url TEXT NOT NULL" in schema:
                print("✅ post_url 字段保留，但没有 UNIQUE 约束")
            else:
                print("⚠️  post_url 字段结构异常")
                return False
            
            # 检查数据完整性
            cursor.execute("SELECT COUNT(*) FROM tasks")
            count = cursor.fetchone()[0]
            print(f"✅ 数据完整性检查: {count} 个任务")
            
            # 测试插入重复URL（应该成功）
            test_url = "https://test.com/duplicate-test"
            try:
                cursor.execute("INSERT INTO tasks (id, post_url, forum_domain, monitor_interval, ai_analysis_enabled, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                             ("test1", test_url, "test.com", 60, True, "pending", "2024-01-01T00:00:00"))
                cursor.execute("INSERT INTO tasks (id, post_url, forum_domain, monitor_interval, ai_analysis_enabled, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                             ("test2", test_url, "test.com", 60, True, "pending", "2024-01-01T00:00:00"))
                
                print("✅ 重复URL插入测试成功")
                
                # 清理测试数据
                cursor.execute("DELETE FROM tasks WHERE id IN ('test1', 'test2')")
                conn.commit()
                
            except sqlite3.IntegrityError as e:
                print(f"❌ 重复URL插入测试失败: {e}")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("数据库迁移：删除 post_url UNIQUE 约束\n")
    
    # 查找数据库文件
    possible_db_paths = [
        "crawling_tasks.db",
        "tasks.db",
        "task_management.db",
        "crawler_tasks.db"
    ]
    
    db_path = None
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件")
        print("请确保以下文件之一存在:")
        for path in possible_db_paths:
            print(f"  - {path}")
        return
    
    print(f"📁 使用数据库文件: {db_path}")
    
    # 执行迁移
    if remove_unique_constraint(db_path):
        if verify_migration(db_path):
            print("\n🎉 迁移完成！")
            print("✅ post_url 字段的 UNIQUE 约束已删除")
            print("✅ 现在可以正常更新任务URL进行翻页")
            print("✅ 数据完整性已验证")
        else:
            print("\n❌ 迁移验证失败")
    else:
        print("\n❌ 迁移失败")


if __name__ == "__main__":
    main()
