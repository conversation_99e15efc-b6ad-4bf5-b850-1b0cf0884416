#!/usr/bin/env python3
"""
页面导航管理器
处理论坛翻页逻辑，支持从指定页面开始监控
"""

import re
import time
import logging
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from DrissionPage import ChromiumPage


class PageNavigationManager:
    """页面导航管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("page_navigation")
        
        # 不同论坛的配置
        self.forum_configs = {
            "lowendtalk.com": {
                "name": "LowEndTalk",
                "comments_per_page": 30,
                "page_url_pattern": "/p{page}",  # /p2, /p3, etc.
                "first_page_suffix": "",  # 第一页没有后缀
                "next_button_selectors": [
                    "css:a[rel='next']",
                    "css:.Next"
                ],
                "page_info_selectors": [
                    "css:.Pager",
                    "css:.PageNumbers"
                ]
            },
            "nodeseek.com": {
                "name": "NodeSeek", 
                "comments_per_page": 10,
                "page_url_pattern": "?page={page}",  # ?page=2, ?page=3, etc.
                "first_page_suffix": "",
                "next_button_selectors": [
                    "css:a[aria-label='下一页']",
                    "css:.next",
                    "css:a:contains('下一页')"
                ],
                "page_info_selectors": [
                    "css:.pagination",
                    "css:.page-numbers"
                ]
            },
            "hostloc.com": {
                "name": "HostLoc",
                "comments_per_page": 20,
                "page_url_pattern": "-{page}-1.html",  # -2-1.html, -3-1.html, etc.
                "first_page_suffix": "-1-1.html",
                "next_button_selectors": [
                    "css:a.nxt",
                    "css:a:contains('下一页')"
                ],
                "page_info_selectors": [
                    "css:.pg"
                ]
            }
        }
    
    def detect_forum_type(self, url: str) -> str:
        """检测论坛类型"""
        for domain in self.forum_configs.keys():
            if domain in url.lower():
                return domain
        return "generic"
    
    def parse_page_from_url(self, url: str) -> Tuple[str, int]:
        """从URL中解析基础URL和页码"""
        forum_type = self.detect_forum_type(url)
        
        if forum_type == "lowendtalk.com":
            # LowEndTalk: /discussion/207804/title/p20
            match = re.search(r'/p(\d+)$', url)
            if match:
                page_num = int(match.group(1))
                base_url = url[:match.start()]
                return base_url, page_num
            else:
                return url, 1
                
        elif forum_type == "nodeseek.com":
            # NodeSeek: /post-123456-1?page=2
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            page_num = int(query_params.get('page', [1])[0])
            
            # 移除page参数构建基础URL
            if 'page' in query_params:
                del query_params['page']
            
            new_query = urlencode(query_params, doseq=True)
            base_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))
            
            return base_url, page_num
            
        elif forum_type == "hostloc.com":
            # HostLoc: /thread-123456-2-1.html
            match = re.search(r'-(\d+)-1\.html$', url)
            if match:
                page_num = int(match.group(1))
                base_url = re.sub(r'-\d+-1\.html$', '', url)
                return base_url, page_num
            else:
                return url, 1
        
        # 通用处理
        return url, 1
    
    def build_page_url(self, base_url: str, page_num: int, forum_type: str = None) -> str:
        """构建指定页码的URL"""
        if not forum_type:
            forum_type = self.detect_forum_type(base_url)
        
        if forum_type not in self.forum_configs:
            return base_url
        
        config = self.forum_configs[forum_type]
        
        if page_num <= 1:
            return base_url + config["first_page_suffix"]
        
        if forum_type == "lowendtalk.com":
            return base_url + f"/p{page_num}"
            
        elif forum_type == "nodeseek.com":
            parsed = urlparse(base_url)
            query_params = parse_qs(parsed.query)
            query_params['page'] = [str(page_num)]
            
            new_query = urlencode(query_params, doseq=True)
            return urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))
            
        elif forum_type == "hostloc.com":
            return base_url + f"-{page_num}-1.html"
        
        return base_url
    
    def calculate_page_from_comment_count(self, comment_count: int, forum_type: str) -> int:
        """根据评论数量计算页码"""
        if forum_type not in self.forum_configs:
            return 1
        
        comments_per_page = self.forum_configs[forum_type]["comments_per_page"]
        return max(1, (comment_count + comments_per_page - 1) // comments_per_page)
    
    def get_expected_comment_count_for_page(self, page_num: int, forum_type: str) -> int:
        """获取指定页面应有的评论数量"""
        if forum_type not in self.forum_configs:
            return 0
        
        comments_per_page = self.forum_configs[forum_type]["comments_per_page"]
        return (page_num - 1) * comments_per_page
    
    def has_next_page(self, page: ChromiumPage, forum_type: str) -> bool:
        """检查是否有下一页"""
        if forum_type not in self.forum_configs:
            return False

        config = self.forum_configs[forum_type]

        # 检查下一页按钮
        for selector in config["next_button_selectors"]:
            try:
                next_button = page.ele(selector, timeout=1)
                if next_button:
                        # 额外检查按钮文本或类名，确保不是禁用状态
                        button_class = next_button.attr('class') or ''
                        button_has_class = next_button.attr('aria-disabled') or ''

                        # 如果按钮包含禁用相关的类名或文本，则认为没有下一页
                        if ('disabled' in button_class.lower() or
                            'inactive' in button_class.lower() or
                            'true' in button_has_class):
                            continue

                        self.logger.debug(f"找到可用的下一页按钮: {selector}")
                        return True
            except Exception as e:
                self.logger.debug(f"检查下一页按钮失败 {selector}: {e}")
                continue

        return False



    def is_last_page(self, page: ChromiumPage, forum_type: str) -> bool:
        """检查是否为最后一页"""
        if forum_type not in self.forum_configs:
            return False

        # 方法1: 检查分页器
        if not self.has_next_page(page, forum_type):
            self.logger.debug("未找到下一页按钮，确认为最后一页")
            return False

        self.logger.debug("找到下一页按钮，当前页不是最后一页")
        return True

    def get_current_page_comment_count(self, page: ChromiumPage, forum_type: str) -> int:
        """获取当前页面的评论数量"""
        # 这里可以根据不同论坛的特点来计算
        # 暂时返回一个估算值，实际使用时需要结合具体的评论提取逻辑
        try:
            if forum_type == "lowendtalk.com":
                comments = page.eles('css:.Item.Comment', timeout=3)
                return len(comments)
            elif forum_type == "nodeseek.com":
                comments = page.eles('css:article', timeout=3)
                return max(0, len(comments) - 1)  # 排除主帖
            elif forum_type == "hostloc.com":
                comments = page.eles('css:.plhin', timeout=3)
                return max(0, len(comments) - 1)  # 排除主帖
        except Exception as e:
            self.logger.debug(f"获取页面评论数量失败: {e}")
        
        return 0
    
    def navigate_to_next_page(self, page: ChromiumPage, current_url: str) -> Optional[str]:
        """导航到下一页并返回新URL"""
        forum_type = self.detect_forum_type(current_url)
        base_url, current_page = self.parse_page_from_url(current_url)

        # 构建下一页URL
        next_page_num = current_page + 1
        next_url = self.build_page_url(base_url, next_page_num, forum_type)
        try:
            # 导航到下一页
            self.logger.info(f"尝试导航到第 {next_page_num} 页: {next_url}")
            page.get(next_url)
            return next_url

        except Exception as e:
            self.logger.error(f"导航到下一页失败: {e}")
            return None
    

    
    def get_forum_config(self, forum_type: str) -> Dict:
        """获取论坛配置"""
        return self.forum_configs.get(forum_type, {})


# 全局实例
page_navigation_manager = PageNavigationManager()
