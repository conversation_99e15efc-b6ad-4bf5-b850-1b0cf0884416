#!/usr/bin/env python3
"""
快速邮件修复
暂时禁用优化邮件服务，使用原始邮件服务，但修复重复发送问题
"""

import os


def disable_optimized_email_service():
    """暂时禁用优化的邮件服务"""
    print("=== 禁用优化邮件服务 ===\n")
    
    # 修改任务执行引擎，暂时禁用优化邮件服务
    task_engine_file = "task_execution_engine.py"
    
    if not os.path.exists(task_engine_file):
        print(f"❌ 文件不存在: {task_engine_file}")
        return False
    
    try:
        # 读取文件
        with open(task_engine_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 暂时禁用优化邮件服务
        content = content.replace(
            'OPTIMIZED_EMAIL_AVAILABLE = True',
            'OPTIMIZED_EMAIL_AVAILABLE = False  # 暂时禁用'
        )
        
        # 如果没找到上面的替换，尝试其他方式
        if 'OPTIMIZED_EMAIL_AVAILABLE = False' not in content:
            content = content.replace(
                'from optimized_email_service import optimized_email_service',
                '# from optimized_email_service import optimized_email_service  # 暂时禁用'
            )
            content = content.replace(
                'OPTIMIZED_EMAIL_AVAILABLE = True',
                'OPTIMIZED_EMAIL_AVAILABLE = False'
            )
            
            # 添加禁用标记
            if 'OPTIMIZED_EMAIL_AVAILABLE' not in content:
                import_section = content.find('# 导入优化的邮件服务')
                if import_section != -1:
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if '# 导入优化的邮件服务' in line:
                            lines.insert(i + 4, 'OPTIMIZED_EMAIL_AVAILABLE = False  # 暂时禁用优化邮件服务')
                            break
                    content = '\n'.join(lines)
        
        # 写回文件
        with open(task_engine_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 优化邮件服务已暂时禁用")
        return True
        
    except Exception as e:
        print(f"❌ 禁用失败: {e}")
        return False


def verify_email_logic():
    """验证邮件逻辑"""
    print("\n=== 验证邮件逻辑 ===\n")
    
    task_engine_file = "task_execution_engine.py"
    
    try:
        with open(task_engine_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            ("汇总邮件逻辑", "发现 {len(new_comments_content)} 条新评论" in content),
            ("单次邮件发送", "# 发送一封汇总邮件" in content),
            ("评论收集", "new_comments_content = []" in content),
            ("优化服务禁用", "OPTIMIZED_EMAIL_AVAILABLE = False" in content or "# 暂时禁用" in content)
        ]
        
        all_good = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'通过' if result else '未找到'}")
            if not result:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def show_current_status():
    """显示当前状态"""
    print("\n=== 当前修复状态 ===\n")
    
    print("✅ 已修复的问题:")
    print("  1. 重复邮件发送 -> 改为汇总邮件（多条评论发送1封邮件）")
    print("  2. 邮件轰炸问题 -> 大幅减少邮件数量")
    print("  3. 评论处理逻辑 -> 收集所有新评论后统一发送")
    print()
    
    print("🔧 临时措施:")
    print("  1. 暂时禁用优化邮件服务（避免SMTP连接问题）")
    print("  2. 使用原始邮件服务（更稳定）")
    print("  3. 保持汇总邮件逻辑（减少邮件数量）")
    print()
    
    print("📊 预期效果:")
    print("  ✅ 不再出现邮件轰炸")
    print("  ✅ SMTP连接更稳定")
    print("  ✅ 邮件通知功能保持正常")
    print("  ✅ 系统运行更稳定")
    print()
    
    print("🔮 后续优化:")
    print("  1. 等系统稳定后，可以重新启用优化邮件服务")
    print("  2. 进一步优化SMTP连接池")
    print("  3. 添加更多邮件发送限制")


def main():
    """主函数"""
    print("快速邮件修复\n")
    
    # 1. 禁用优化邮件服务
    if disable_optimized_email_service():
        print("✅ 优化邮件服务已禁用")
    else:
        print("❌ 禁用优化邮件服务失败")
        return
    
    # 2. 验证邮件逻辑
    if verify_email_logic():
        print("✅ 邮件逻辑验证通过")
    else:
        print("⚠️  邮件逻辑验证部分失败")
    
    # 3. 显示当前状态
    show_current_status()
    
    print("\n🎉 快速修复完成！")
    print("现在系统应该:")
    print("✅ 不再发送重复邮件")
    print("✅ 使用更稳定的邮件服务")
    print("✅ 避免SMTP连接问题")
    print("\n请重启任务执行引擎以应用修复。")


if __name__ == "__main__":
    main()
